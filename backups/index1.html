<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Bytewise Research Assistant</title>
  <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <style>
    * {
      box-sizing: border-box;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }
    body {
      max-width: 900px;
      margin: 0 auto;
      padding: 20px;
      background: linear-gradient(135deg, #f5f7fa 0%, #e4edf5 100%);
      color: #2c3e50;
      line-height: 1.6;
    }
    .app-container {
      background: white;
      border-radius: 15px;
      box-shadow: 0 8px 30px rgba(0,0,0,0.12);
      padding: 30px;
      margin-top: 20px;
      position: relative;
      overflow: hidden;
    }
    .app-container::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 5px;
      background: linear-gradient(90deg, #3498db, #2ecc71, #e74c3c);
    }
    h1 {
      color: #2c3e50;
      text-align: center;
      margin-bottom: 10px;
      font-weight: 700;
      font-size: 2.2rem;
      background: linear-gradient(90deg, #3498db, #2c3e50);
      -webkit-background-clip: text;
      background-clip: text;
      color: transparent;
    }
    .subtitle {
      text-align: center;
      color: #7f8c8d;
      margin-bottom: 30px;
      font-size: 1.1rem;
    }
    .mode-toggle {
      display: flex;
      margin-bottom: 30px;
      border-radius: 10px;
      overflow: hidden;
      box-shadow: 0 4px 6px rgba(0,0,0,0.08);
    }
    .mode-toggle button {
      flex: 1;
      padding: 16px;
      background: #f0f0f0;
      border: none;
      cursor: pointer;
      font-weight: 600;
      font-size: 17px;
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 10px;
    }
    .mode-toggle button:hover {
      background: #e0e0e0;
    }
    .mode-toggle button.active {
      background: #3498db;
      color: white;
      box-shadow: 0 4px 10px rgba(52, 152, 219, 0.3);
    }
    .mode-toggle button:first-child {
      border-radius: 10px 0 0 10px;
    }
    .mode-toggle button:last-child {
      border-radius: 0 10px 10px 0;
    }
    .upload-area {
      border: 3px dashed #3498db;
      border-radius: 12px;
      padding: 40px 30px;
      text-align: center;
      margin: 25px 0;
      background: #f8fafd;
      cursor: pointer;
      transition: all 0.3s;
      position: relative;
    }
    .upload-area:hover {
      background: #e3f2fd;
      transform: translateY(-3px);
    }
    .upload-area.dragover {
      background: #d1e7ff;
      border-color: #2980b9;
    }
    .upload-icon {
      font-size: 48px;
      color: #3498db;
      margin-bottom: 15px;
    }
    .btn {
      padding: 14px 28px;
      background: #3498db;
      color: white;
      border: none;
      border-radius: 8px;
      cursor: pointer;
      font-size: 17px;
      font-weight: 600;
      margin: 10px 5px;
      transition: all 0.3s;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
      box-shadow: 0 4px 6px rgba(52, 152, 219, 0.2);
    }
    .btn:hover {
      background: #2980b9;
      transform: translateY(-2px);
      box-shadow: 0 6px 8px rgba(52, 152, 219, 0.3);
    }
    .btn:disabled {
      background: #bdc3c7;
      cursor: not-allowed;
      transform: none;
      box-shadow: none;
    }
    .btn i {
      font-size: 1.2em;
    }
    .btn-secondary {
      background: #2ecc71;
      box-shadow: 0 4px 6px rgba(46, 204, 113, 0.2);
    }
    .btn-secondary:hover {
      background: #27ae60;
      box-shadow: 0 6px 8px rgba(46, 204, 113, 0.3);
    }
    .btn-tertiary {
      background: #e74c3c;
      box-shadow: 0 4px 6px rgba(231, 76, 60, 0.2);
    }
    .btn-tertiary:hover {
      background: #c0392b;
      box-shadow: 0 6px 8px rgba(231, 76, 60, 0.3);
    }
    textarea, input[type="text"] {
      width: 100%;
      padding: 15px;
      border: 2px solid #e0e6ed;
      border-radius: 10px;
      font-size: 16px;
      margin: 15px 0;
      transition: border-color 0.3s;
    }
    textarea:focus, input[type="text"]:focus {
      outline: none;
      border-color: #3498db;
      box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
    }
    .status-message {
      padding: 15px;
      border-radius: 8px;
      margin: 20px 0;
      text-align: center;
      font-size: 16px;
      font-weight: 500;
    }
    .success {
      background: #d4edda;
      color: #155724;
      border-left: 5px solid #28a745;
    }
    .error {
      background: #f8d7da;
      color: #721c24;
      border-left: 5px solid #dc3545;
    }
    .processing {
      background: #cce5ff;
      color: #004085;
      border-left: 5px solid #007bff;
    }
    .results-container {
      margin-top: 25px;
      padding: 25px;
      border: 1px solid #e0e6ed;
      border-radius: 12px;
      background: #f9fbfd;
      box-shadow: inset 0 2px 4px rgba(0,0,0,0.04);
    }
    .results-container h3 {
      margin-top: 0;
      color: #2c3e50;
      border-bottom: 2px solid #e0e6ed;
      padding-bottom: 15px;
      display: flex;
      align-items: center;
      gap: 10px;
    }
    .point-item {
      background: white;
      padding: 18px;
      margin: 15px 0;
      border-left: 5px solid #3498db;
      border-radius: 8px;
      box-shadow: 0 3px 6px rgba(0,0,0,0.05);
      position: relative;
      transition: transform 0.3s;
    }
    .point-item:hover {
      transform: translateX(5px);
    }
    .discussion-box {
      background: #fffde7;
      padding: 25px;
      border-radius: 12px;
      margin-top: 25px;
      white-space: pre-wrap;
      line-height: 1.7;
      border-left: 5px solid #ffc107;
      box-shadow: 0 4px 6px rgba(0,0,0,0.05);
    }
    .hidden {
      display: none;
    }
    .progress-bar {
      height: 8px;
      background: #e0e6ed;
      border-radius: 4px;
      overflow: hidden;
      margin: 20px 0;
    }
    .progress-fill {
      height: 100%;
      background: #3498db;
      width: 0%;
      transition: width 0.5s ease;
    }
    .file-info {
      background: #e3f2fd;
      padding: 12px;
      border-radius: 8px;
      margin: 15px 0;
      display: flex;
      align-items: center;
      gap: 10px;
    }
    .loading-spinner {
      display: inline-block;
      width: 20px;
      height: 20px;
      border: 3px solid rgba(255,255,255,0.3);
      border-radius: 50%;
      border-top-color: white;
      animation: spin 1s ease-in-out infinite;
    }
    @keyframes spin {
      to { transform: rotate(360deg); }
    }
    .action-buttons {
      display: flex;
      gap: 10px;
      flex-wrap: wrap;
      margin-top: 20px;
    }
    .logo {
      text-align: center;
      margin-bottom: 10px;
    }
    .logo-icon {
      font-size: 36px;
      color: #3498db;
    }
    @media (max-width: 768px) {
      body {
        padding: 10px;
      }
      .app-container {
        padding: 20px;
      }
      .mode-toggle {
        flex-direction: column;
      }
      .mode-toggle button {
        border-radius: 10px !important;
        margin-bottom: 5px;
      }
      .btn {
        width: 100%;
        margin: 5px 0;
      }
      .action-buttons {
        flex-direction: column;
      }
    }
  </style>
</head>
<body>
  <div id="app" class="app-container">
    <div class="logo">
      <i class="fas fa-book-open logo-icon"></i>
    </div>
    <h1>Bytewise Research Assistant</h1>
    <p class="subtitle">Upload, analyze, and discuss research papers with AI-powered insights</p>
    
    <div class="mode-toggle">
      <button 
        @click="setMode('review')" 
        :class="{ active: mode === 'review' }"
      >
        <i class="fas fa-file-upload"></i> Review Mode
      </button>
      <button 
        @click="setMode('write')" 
        :class="{ active: mode === 'write' }"
      >
        <i class="fas fa-edit"></i> Write New
      </button>
    </div>

    <!-- Review Mode -->
    <div v-if="mode === 'review'">
      <h2><i class="fas fa-microscope"></i> Upload Research Paper</h2>
      
      <div 
        class="upload-area"
        :class="{ 'dragover': isDragging }"
        @dragover.prevent="handleDragOver"
        @dragleave="isDragging = false"
        @drop.prevent="handleDrop"
        @click="triggerFileInput"
      >
        <div class="upload-icon">
          <i class="fas fa-cloud-upload-alt"></i>
        </div>
        <p v-if="!file">Drag & drop your PDF here or click to browse</p>
        <div v-else class="file-info">
          <i class="fas fa-file-pdf"></i>
          <div>
            <strong>{{ file.name }}</strong>
            <p>{{ formatFileSize(file.size) }}</p>
          </div>
        </div>
        <input 
          type="file" 
          ref="fileInput" 
          @change="handleFileSelect" 
          accept=".pdf" 
          class="hidden"
        >
      </div>
      
      <div class="action-buttons">
        <button 
          class="btn" 
          @click="processPaper" 
          :disabled="!file || isProcessing"
        >
          <span v-if="isProcessing" class="loading-spinner"></span>
          <span v-else><i class="fas fa-cogs"></i> Process Paper</span>
        </button>
      </div>
      
      <div v-if="isProcessing" class="progress-bar">
        <div class="progress-fill" :style="{ width: progress + '%' }"></div>
      </div>
      
      <div v-if="statusMessage" :class="['status-message', statusClass]">
        <i v-if="statusClass === 'success'" class="fas fa-check-circle"></i>
        <i v-if="statusClass === 'error'" class="fas fa-exclamation-circle"></i>
        <i v-if="statusClass === 'processing'" class="fas fa-sync-alt fa-spin"></i>
        {{ statusMessage }}
      </div>
      
      <div v-if="paperPoints.length" class="results-container">
        <h3><i class="fas fa-list-ul"></i> Generated Points:</h3>
        <div v-for="(point, index) in paperPoints" :key="index" class="point-item">
          {{ point }}
        </div>
      </div>
    </div>

    <!-- Write New Mode -->
    <div v-if="mode === 'write'">
      <h2><i class="fas fa-lightbulb"></i> Research Prompt</h2>
      
      <textarea 
        v-model="prompt" 
        placeholder="Enter your research question or topic (e.g., 'Impact of AI on climate change', 'Recent advances in quantum computing')..." 
        rows="5"
      ></textarea>
      
      <div class="action-buttons">
        <button 
          class="btn btn-secondary" 
          @click="generatePoints" 
          :disabled="!prompt.trim() || isGenerating"
        >
          <span v-if="isGenerating" class="loading-spinner"></span>
          <span v-else><i class="fas fa-bolt"></i> Generate Research Points</span>
        </button>
      </div>
      
      <div v-if="isGenerating" class="progress-bar">
        <div class="progress-fill" :style="{ width: progress + '%' }"></div>
      </div>
      
      <div v-if="generatedPoints.length" class="results-container">
        <h3><i class="fas fa-star"></i> Generated Points:</h3>
        <div v-for="(point, index) in generatedPoints" :key="index" class="point-item">
          {{ point }}
        </div>
        
        <div class="action-buttons">
          <button 
            class="btn btn-tertiary" 
            @click="startDiscussion" 
            :disabled="isDiscussing"
          >
            <span v-if="isDiscussing" class="loading-spinner"></span>
            <span v-else><i class="fas fa-comments"></i> Start Discussion</span>
          </button>
        </div>
      </div>
      
      <div v-if="discussion" class="discussion-box">
        <h3><i class="fas fa-comment-dots"></i> Discussion:</h3>
        <p>{{ discussion }}</p>
      </div>
    </div>
  </div>

  <script>
    const { createApp, ref, computed } = Vue
    
    createApp({
      setup() {
        const mode = ref('review')
        const file = ref(null)
        const isDragging = ref(false)
        const isProcessing = ref(false)
        const isGenerating = ref(false)
        const isDiscussing = ref(false)
        const progress = ref(0)
        const statusMessage = ref('')
        const paperPoints = ref([])
        const prompt = ref('')
        const generatedPoints = ref([])
        const discussion = ref('')
        
        const statusClass = computed(() => {
          if (statusMessage.value.includes('success')) return 'success'
          if (statusMessage.value.includes('Error') || statusMessage.value.includes('Failed')) return 'error'
          return 'processing'
        })
        
        function setMode(newMode) {
          mode.value = newMode
          if (newMode === 'review') {
            generatedPoints.value = []
            discussion.value = ''
            prompt.value = ''
          } else {
            paperPoints.value = []
            statusMessage.value = ''
          }
        }
        
        function formatFileSize(bytes) {
          if (bytes === 0) return '0 Bytes'
          const k = 1024
          const sizes = ['Bytes', 'KB', 'MB', 'GB']
          const i = Math.floor(Math.log(bytes) / Math.log(k))
          return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
        }
        
        function triggerFileInput() {
          this.$refs.fileInput.click()
        }
        
        function handleFileSelect(event) {
          if (event.target.files.length) {
            file.value = event.target.files[0]
          }
        }
        
        function handleDragOver(event) {
          isDragging.value = true
          event.dataTransfer.dropEffect = 'copy'
        }
        
        function handleDrop(event) {
          isDragging.value = false
          const files = event.dataTransfer.files
          if (files.length && files[0].type === 'application/pdf') {
            file.value = files[0]
          } else {
            statusMessage.value = 'Please drop a PDF file'
          }
        }
        
        async function processPaper() {
          if (!file.value) return
          
          isProcessing.value = true
          progress.value = 30
          statusMessage.value = 'Uploading and processing paper...'
          
          try {
            const formData = new FormData()
            formData.append('file', file.value)
            
            const response = await fetch('/upload', {
              method: 'POST',
              body: formData
            })
            
            progress.value = 70
            
            const data = await response.json()
            
            if (response.ok) {
              statusMessage.value = 'Paper processed successfully! Extracted key points.'
              paperPoints.value = data.points
            } else {
              statusMessage.value = `Error: ${data.error || 'Failed to process paper'}`
            }
          } catch (error) {
            statusMessage.value = `Network error: ${error.message}`
          } finally {
            progress.value = 100
            setTimeout(() => {
              isProcessing.value = false
              progress.value = 0
            }, 500)
          }
        }
        
        async function generatePoints() {
          if (!prompt.value.trim()) return
          
          isGenerating.value = true
          progress.value = 30
          generatedPoints.value = []
          discussion.value = ''
          
          try {
            const response = await fetch('/generate', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({ prompt: prompt.value })
            })
            
            progress.value = 70
            
            const data = await response.json()
            
            if (response.ok) {
              generatedPoints.value = data.points
            } else {
              generatedPoints.value = [`Error: ${data.error || 'Failed to generate points'}`]
            }
          } catch (error) {
            generatedPoints.value = [`Network error: ${error.message}`]
          } finally {
            progress.value = 100
            setTimeout(() => {
              isGenerating.value = false
              progress.value = 0
            }, 500)
          }
        }
        
        async function startDiscussion() {
          if (!prompt.value.trim() || !generatedPoints.value.length) return
          
          isDiscussing.value = true
          progress.value = 30
          discussion.value = 'Analyzing research points and generating discussion...'
          
          try {
            const response = await fetch('/discuss', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({ prompt: prompt.value })
            })
            
            progress.value = 70
            
            const data = await response.json()
            
            if (response.ok) {
              discussion.value = data.discussion
            } else {
              discussion.value = `Error: ${data.error || 'Failed to start discussion'}`
            }
          } catch (error) {
            discussion.value = `Network error: ${error.message}`
          } finally {
            progress.value = 100
            setTimeout(() => {
              isDiscussing.value = false
              progress.value = 0
            }, 500)
          }
        }
        
        return {
          mode,
          file,
          isDragging,
          isProcessing,
          isGenerating,
          isDiscussing,
          progress,
          statusMessage,
          paperPoints,
          prompt,
          generatedPoints,
          discussion,
          statusClass,
          setMode,
          formatFileSize,
          triggerFileInput,
          handleFileSelect,
          handleDragOver,
          handleDrop,
          processPaper,
          generatePoints,
          startDiscussion
        }
      }
    }).mount('#app')
  </script>
</body>
</html>