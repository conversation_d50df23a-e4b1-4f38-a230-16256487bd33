import logging
from transformers import pipeline, AutoTokenizer, AutoModelForCausalLM
from typing import List, AsyncGenerator
import asyncio

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class LocalLLMClient:
    def __init__(self):
        # Use a smaller model that can run on CPU
        self.model_name = "gpt2"
        logger.info(f"Loading LLM model: {self.model_name}")
        
        try:
            # Initialize text generation pipeline
            self.generator = pipeline(
                "text-generation", 
                model=self.model_name,
                tokenizer=self.model_name,
                device=-1  # Use CPU
            )
            logger.info("LLM model loaded successfully")
        except Exception as e:
            logger.error(f"Failed to load LLM model: {str(e)}")
            raise

    async def generate_points(self, text: str) -> List[str]:
        """Generate research points from paper text"""
        try:
            prompt = f"Extract key research points from the following text:\n\n{text[:2000]}\n\nKey points:"
            results = await self._generate_text(prompt, max_length=500)
            points = results[0]['generated_text'].split('\n')
            return [p.strip() for p in points if p.strip()][:5]  # Return first 5 points
        except Exception as e:
            logger.error(f"Point generation failed: {str(e)}")
            return ["Error generating points"]

    async def generate_from_prompt(self, prompt: str, context: str) -> List[str]:
        """Generate research points from prompt and context"""
        try:
            full_prompt = (
                f"Based on the research context, generate points about: {prompt}\n\n"
                f"Context: {context[:1500]}\n\nPoints:"
            )
            results = await self._generate_text(full_prompt, max_length=600)
            points = results[0]['generated_text'].split('\n')
            return [p.strip() for p in points if p.strip()][:3]  # Return first 3 points
        except Exception as e:
            logger.error(f"Prompt-based generation failed: {str(e)}")
            return [f"Error: {str(e)}"]

    async def rag_discussion(self, prompt: str, documents: List[str]) -> str:
        """Generate discussion using RAG approach"""
        try:
            # Combine documents with length limit
            context = "\n\n".join([doc[:1000] for doc in documents])[:5000]
            
            full_prompt = (
                f"Discuss the research topic: {prompt}\n\n"
                f"Research Context:\n{context}\n\n"
                "Discussion:"
            )
            results = await self._generate_text(full_prompt, max_length=800)
            return results[0]['generated_text']
        except Exception as e:
            logger.error(f"RAG discussion failed: {str(e)}")
            return f"Error generating discussion: {str(e)}"

    async def _generate_text(self, prompt: str, max_length: int = 300) -> List[dict]:
        """Generate text with the local model asynchronously"""
        loop = asyncio.get_running_loop()
        return await loop.run_in_executor(
            None, 
            lambda: self.generator(
                prompt,
                max_length=max_length,
                num_return_sequences=1,
                truncation=True,
                pad_token_id=self.generator.tokenizer.eos_token_id
            )
        )