import os
import uuid
import tempfile
from fastapi import FastAPI, File, UploadFile, HTTPException, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse, FileResponse, JSONResponse
from mineru_processor import process_pdf_with_mineru
from database import store_paper, get_relevant_papers, init_db
from schemas import GenerateRequest
from config import settings
import logging
import sqlite3
import json
from llm_client import LocalLLMClient  # Import the new LLM client

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(title="Bytewise Research Assistant")

# CORS configuration
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize database
init_db()

# Get absolute path to frontend
current_dir = os.path.dirname(os.path.abspath(__file__))
frontend_path = "D:\\bytewise module prototyping 2\\frontend"
logger.info(f"Serving frontend from: {frontend_path}")

# Serve frontend files
app.mount("/static", StaticFiles(directory=os.path.join(frontend_path, "static")), name="static")
app.mount("/assets", StaticFiles(directory=os.path.join(frontend_path, "assets")), name="assets")

# Initialize LLM client
llm = LocalLLMClient()

@app.post("/upload")
async def upload_file(file: UploadFile = File(...)):
    if file.content_type != "application/pdf":
        raise HTTPException(status_code=400, detail="Only PDF files are allowed")
    
    try:
        # Create a temp file
        pdf_id = str(uuid.uuid4())
        temp_dir = tempfile.mkdtemp()
        pdf_path = os.path.join(temp_dir, f"{pdf_id}.pdf")
        
        # Save uploaded file
        contents = await file.read()
        with open(pdf_path, "wb") as f:
            f.write(contents)
        
        # Process with Mineru
        mineru_output_dir = os.path.join(settings.mineru_output_dir, pdf_id)
        os.makedirs(mineru_output_dir, exist_ok=True)
        
        text_content = process_pdf_with_mineru(pdf_path, mineru_output_dir)
        
        # Generate points using the LLM client
        points = await llm.generate_points(text_content)
        
        # Store in database
        store_paper(pdf_id, file.filename, text_content, points)
        
        # Cleanup
        os.unlink(pdf_path)
        os.rmdir(temp_dir)
        
        return {"status": "success", "points": points}
        
    except Exception as e:
        logger.error(f"Upload failed: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/generate")
async def generate_points(request: GenerateRequest):
    try:
        # First find relevant papers based on the prompt
        relevant_papers = get_relevant_papers(request.prompt)
        
        if not relevant_papers:
            return {"points": ["No papers in database yet. Upload papers in Review Mode first."]}
        
        points = []
        # For each relevant paper, generate more detailed points
        for paper in relevant_papers:
            # Get the full paper content from database
            conn = sqlite3.connect(settings.database_path)
            c = conn.cursor()
            c.execute("SELECT content FROM papers WHERE id = ?", (paper['id'],))
            full_content = c.fetchone()[0]
            conn.close()
            
            # Generate detailed points using the full content
            paper_points = await llm.generate_from_prompt(request.prompt, full_content)
            points.extend(paper_points)
        
        return {"points": points}
    except Exception as e:
        logger.error(f"Generate failed: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/discuss")
async def discuss(request: GenerateRequest):
    try:
        # Get relevant papers
        relevant_papers = get_relevant_papers(request.prompt)
        
        if not relevant_papers:
            return {"discussion": "No relevant papers found in database. Upload papers first."}
        
        # Get full content of relevant papers
        documents = []
        conn = sqlite3.connect(settings.database_path)
        c = conn.cursor()
        for paper in relevant_papers:
            c.execute("SELECT content FROM papers WHERE id = ?", (paper['id'],))
            result = c.fetchone()
            if result:
                documents.append(result[0])
        conn.close()
        
        if not documents:
            return {"discussion": "No content found for relevant papers."}
        
        # Generate discussion using RAG
        discussion = await llm.rag_discussion(request.prompt, documents)
        return {"discussion": discussion}
    except Exception as e:
        logger.error(f"Discuss failed: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# Database endpoints
@app.get("/database", response_model=dict)
async def view_database():
    try:
        conn = sqlite3.connect(settings.database_path)
        c = conn.cursor()
        c.execute("SELECT id, filename, content, points FROM papers")
        papers = c.fetchall()
        
        formatted = []
        for paper in papers:
            formatted.append({
                "id": paper[0],
                "filename": paper[1],
                "content_preview": paper[2][:100] + "..." if paper[2] else "",
                "content_length": len(paper[2]) if paper[2] else 0,
                "points": json.loads(paper[3]) if paper[3] else []
            })
        
        conn.close()
        return {"papers": formatted}
    
    except Exception as e:
        logger.error(f"Database view failed: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/database/{paper_id}", response_model=dict)
async def view_paper_details(paper_id: str):
    try:
        conn = sqlite3.connect(settings.database_path)
        c = conn.cursor()
        c.execute("SELECT id, filename, content, points FROM papers WHERE id = ?", (paper_id,))
        paper = c.fetchone()
        
        if not paper:
            raise HTTPException(status_code=404, detail="Paper not found")
        
        formatted = {
            "id": paper[0],
            "filename": paper[1],
            "content": paper[2],
            "points": json.loads(paper[3]) if paper[3] else []
        }
        
        conn.close()
        return formatted
    
    except Exception as e:
        logger.error(f"Paper details failed: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# Serve frontend
@app.get("/")
async def serve_index():
    return FileResponse(os.path.join(frontend_path, "index.html"))

@app.get("/{full_path:path}")
async def serve_spa(full_path: str):
    return FileResponse(os.path.join(frontend_path, "index.html"))

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="127.0.0.1", port=8000)