import os
import uuid
import tempfile
from fastapi import FastAP<PERSON>, File, UploadFile, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import FileResponse, JSONResponse
from mineru_processor import process_pdf_with_mineru
from database import store_paper, get_relevant_papers, init_db
from schemas import GenerateRequest
from config import settings
import logging
import sqlite3
import json
import aiohttp

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(title="Bytewise Research Assistant")

# CORS configuration
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize database
init_db()

# Frontend setup
frontend_path = "D:\\bytewise module prototyping 2\\frontend"
app.mount("/static", StaticFiles(directory=os.path.join(frontend_path, "static")), name="static")
app.mount("/assets", StaticFiles(directory=os.path.join(frontend_path, "assets")), name="assets")

class OllamaClient:
    def __init__(self):
        self.base_url = "http://localhost:11434"
        self.model = "llama3:8b-instruct-q4_K_M"
        self.max_context_tokens = 6144  # Safe for 12GB VRAM

    async def generate(self, prompt: str, max_tokens: int = 500, context: str = "") -> str:
        try:
            full_prompt = f"{context}\n\n{prompt}" if context else prompt
            async with aiohttp.ClientSession() as session:
                payload = {
                    "model": self.model,
                    "prompt": full_prompt[:self.max_context_tokens],
                    "stream": False,
                    "options": {
                        "temperature": 0.7,
                        "num_ctx": self.max_context_tokens,
                        "max_tokens": min(max_tokens, 2000)
                    }
                }
                async with session.post(f"{self.base_url}/api/generate", json=payload) as response:
                    data = await response.json()
                    return data.get("response", "").strip()
        except Exception as e:
            logger.error(f"Ollama error: {str(e)}")
            return f"LLM Error: {str(e)}"

# Initialize LLM client
llm = OllamaClient()

@app.on_event("startup")
async def startup_event():
    logger.info("Initializing LLM client...")
    try:
        test_response = await llm.generate("Test connection", max_tokens=10)
        logger.info(f"LLM test response: {test_response[:50]}...")
    except Exception as e:
        logger.error(f"LLM initialization failed: {str(e)}")

# API Endpoints (must come before catch-all route)
@app.post("/upload")
async def upload_file(file: UploadFile = File(...)):
    if file.content_type != "application/pdf":
        raise HTTPException(400, "Only PDFs allowed")

    try:
        pdf_id = str(uuid.uuid4())
        with tempfile.NamedTemporaryFile(delete=False, suffix=".pdf") as tmp:
            tmp.write(await file.read())
            pdf_path = tmp.name

        output_dir = os.path.join(settings.mineru_output_dir, pdf_id)
        os.makedirs(output_dir, exist_ok=True)
        text_content = process_pdf_with_mineru(pdf_path, output_dir)
        os.unlink(pdf_path)

        prompt = f"""Extract 5 key research points from this paper:
        {text_content[:4000]}
        Format each as '- [point]'"""
        response = await llm.generate(prompt)
        points = [line.strip() for line in response.split('\n') if line.startswith('-')]

        store_paper(pdf_id, file.filename, text_content, points)
        return {"status": "success", "points": points[:5]}

    except Exception as e:
        logger.error(f"Upload failed: {str(e)}")
        raise HTTPException(500, str(e))

@app.post("/generate")
async def generate_points(request: GenerateRequest):
    try:
        relevant_papers = get_relevant_papers(request.prompt)
        if not relevant_papers:
            return {"points": ["No matching papers found"]}

        points = []
        for paper in relevant_papers:
            content = paper['content']
            prompt = f"""Generate specific insights about: {request.prompt}
            From this content:
            {content[:4000]}
            Format as bullet points with '-'"""
            
            response = await llm.generate(prompt, max_tokens=800)
            points.extend([line.strip() for line in response.split('\n') if line.startswith('-')])
            
            if len(points) >= 10:
                break

        return {"points": points[:10]}

    except Exception as e:
        logger.error(f"Generation failed: {str(e)}")
        return {"points": ["LLM service error"]}

@app.post("/discuss")
async def discuss(request: GenerateRequest):
    try:
        relevant_papers = get_relevant_papers(request.prompt)
        if not relevant_papers:
            return {"discussion": "No relevant papers found"}

        context = []
        for i, paper in enumerate(relevant_papers[:5]):
            context.append(f"[Source {i+1}]: {paper['content'][:2000]}")

        prompt = f"""Write a research discussion about: {request.prompt}
        Using these sources:
        {'\n'.join(context)}
        Cite sources like [1][2]"""
        discussion = await llm.generate(prompt, max_tokens=1000)
        
        return {"discussion": discussion}

    except Exception as e:
        logger.error(f"Discussion failed: {str(e)}")
        return {"discussion": "Error generating discussion"}

# Database endpoints (must come before catch-all)
@app.get("/database", response_model=dict)
async def view_database():
    try:
        conn = sqlite3.connect(settings.database_path)
        c = conn.cursor()
        
        c.execute("SELECT COUNT(*) FROM papers")
        total_papers = c.fetchone()[0]
        
        c.execute("SELECT id, filename, content, points FROM papers")
        papers = c.fetchall()
        conn.close()
        
        return {
            "papers": [{
                "id": paper[0],
                "filename": paper[1],
                "content_preview": paper[2][:100] + "..." if paper[2] else "",
                "content_length": len(paper[2]) if paper[2] else 0,
                "points": json.loads(paper[3]) if paper[3] else []
            } for paper in papers],
            "total_papers": total_papers
        }
        
    except Exception as e:
        logger.error(f"Database error: {str(e)}")
        raise HTTPException(500, "Database error")

@app.get("/database/{paper_id}", response_model=dict)
async def view_paper_details(paper_id: str):
    try:
        conn = sqlite3.connect(settings.database_path)
        c = conn.cursor()
        c.execute("SELECT id, filename, content, points FROM papers WHERE id = ?", (paper_id,))
        paper = c.fetchone()
        
        if not paper:
            raise HTTPException(404, "Paper not found")
        
        conn.close()
        return {
            "id": paper[0],
            "filename": paper[1],
            "content": paper[2],
            "points": json.loads(paper[3]) if paper[3] else []
        }
    
    except Exception as e:
        logger.error(f"Paper details failed: {str(e)}")
        raise HTTPException(500, str(e))

# Frontend routes (must come last)
@app.get("/")
async def serve_index():
    return FileResponse(os.path.join(frontend_path, "index.html"))

@app.get("/{full_path:path}")
async def serve_spa(full_path: str):
    return FileResponse(os.path.join(frontend_path, "index.html"))

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="127.0.0.1", port=8000)