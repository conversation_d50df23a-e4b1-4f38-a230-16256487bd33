<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Bytewise Research Assistant</title>
  <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
<style>
    * {
      box-sizing: border-box;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }
    body {
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
      background: #121212; /* Dark grey background */
      color: #e0e0e0; /* Light text */
      line-height: 1.6;
    }
    .app-container {
      background: #1e1e1e; /* Dark container */
      border-radius: 15px;
      box-shadow: 0 8px 30px rgba(0,0,0,0.5);
      padding: 30px;
      margin-top: 20px;
      position: relative;
      overflow: hidden;
      border: 1px solid #333;
    }
    .app-container::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 5px;
      background: linear-gradient(90deg, #555, #888, #aaa); /* Grey gradient */
    }
    h1 {
      color: white;
      text-align: center;
      margin-bottom: 10px;
      font-weight: 700;
      font-size: 2.2rem;
      background: linear-gradient(90deg, #888, #aaa);
      -webkit-background-clip: text;
      background-clip: text;
      color: transparent;
    }
    .subtitle {
      text-align: center;
      color: #aaa;
      margin-bottom: 30px;
      font-size: 1.1rem;
    }
    .mode-toggle {
      display: flex;
      margin-bottom: 25px;
      border-radius: 10px;
      overflow: hidden;
      box-shadow: 0 4px 6px rgba(0,0,0,0.3);
    }
    .mode-toggle button {
      flex: 1;
      padding: 16px;
      background: #333;
      border: none;
      cursor: pointer;
      font-weight: 600;
      font-size: 17px;
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 10px;
      color: white;
    }
    .mode-toggle button:hover {
      background: #444;
    }
    .mode-toggle button.active {
      background: #555;
      color: white;
      box-shadow: 0 4px 10px rgba(0,0,0,0.3);
    }
    .upload-area {
      border: 3px dashed #555;
      border-radius: 12px;
      padding: 40px 30px;
      text-align: center;
      margin: 25px 0;
      background: #252525;
      cursor: pointer;
      transition: all 0.3s;
      position: relative;
    }
    .upload-area:hover {
      background: #333;
      transform: translateY(-3px);
    }
    .upload-area.dragover {
      background: #3a3a3a;
      border-color: #777;
    }
    .upload-icon {
      font-size: 48px;
      color: #777;
      margin-bottom: 15px;
    }
    .btn {
      padding: 14px 28px;
      background: #444;
      color: white;
      border: none;
      border-radius: 8px;
      cursor: pointer;
      font-size: 17px;
      font-weight: 600;
      margin: 10px 5px;
      transition: all 0.3s;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
      box-shadow: 0 4px 6px rgba(0,0,0,0.3);
    }
    .btn:hover {
      background: #555;
      transform: translateY(-2px);
      box-shadow: 0 6px 8px rgba(0,0,0,0.4);
    }
    .btn:disabled {
      background: #333;
      cursor: not-allowed;
      transform: none;
      box-shadow: none;
    }
    .btn i {
      font-size: 1.2em;
    }
    .btn-secondary {
      background: #555;
      box-shadow: 0 4px 6px rgba(0,0,0,0.3);
    }
    .btn-secondary:hover {
      background: #666;
      box-shadow: 0 6px 8px rgba(0,0,0,0.4);
    }
    .btn-tertiary {
      background: #666;
      box-shadow: 0 4px 6px rgba(0,0,0,0.3);
    }
    .btn-tertiary:hover {
      background: #777;
      box-shadow: 0 6px 8px rgba(0,0,0,0.4);
    }
    .btn-database {
      background: #555;
      box-shadow: 0 4px 6px rgba(0,0,0,0.3);
    }
    .btn-database:hover {
      background: #666;
      box-shadow: 0 6px 8px rgba(0,0,0,0.4);
    }
    textarea, input[type="text"] {
      width: 100%;
      padding: 15px;
      border: 2px solid #444;
      border-radius: 10px;
      font-size: 16px;
      margin: 15px 0;
      transition: border-color 0.3s;
      background: #252525;
      color: #e0e0e0;
    }
    textarea:focus, input[type="text"]:focus {
      outline: none;
      border-color: #777;
      box-shadow: 0 0 0 3px rgba(119,119,119,0.2);
    }
    .status-message {
      padding: 15px;
      border-radius: 8px;
      margin: 20px 0;
      text-align: center;
      font-size: 16px;
      font-weight: 500;
      background: #252525;
      border-left: 5px solid #777;
    }
    .success {
      background: #1a2a1a;
      color: #a5d6a7;
      border-left: 5px solid #4caf50;
    }
    .error {
      background: #2a1a1a;
      color: #ef9a9a;
      border-left: 5px solid #f44336;
    }
    .processing {
      background: #1a1a2a;
      color: #90caf9;
      border-left: 5px solid #2196f3;
    }
    .results-container {
      margin-top: 25px;
      padding: 25px;
      border: 1px solid #444;
      border-radius: 12px;
      background: #252525;
      box-shadow: inset 0 2px 4px rgba(0,0,0,0.1);
    }
    .results-container h3 {
      margin-top: 0;
      color: white;
      border-bottom: 2px solid #444;
      padding-bottom: 15px;
      display: flex;
      align-items: center;
      gap: 10px;
    }
    .point-item {
      background: #333;
      padding: 18px;
      margin: 15px 0;
      border-left: 5px solid #777;
      border-radius: 8px;
      box-shadow: 0 3px 6px rgba(0,0,0,0.1);
      position: relative;
      transition: transform 0.3s;
    }
    .point-item:hover {
      transform: translateX(5px);
      background: #3a3a3a;
    }
    .discussion-box {
      background: #252525;
      padding: 25px;
      border-radius: 12px;
      margin-top: 25px;
      white-space: pre-wrap;
      line-height: 1.7;
      border-left: 5px solid #888;
      box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    }
    .hidden {
      display: none;
    }
    .progress-bar {
      height: 8px;
      background: #333;
      border-radius: 4px;
      overflow: hidden;
      margin: 20px 0;
    }
    .progress-fill {
      height: 100%;
      background: #777;
      width: 0%;
      transition: width 0.5s ease;
    }
    .file-info {
      background: #333;
      padding: 12px;
      border-radius: 8px;
      margin: 15px 0;
      display: flex;
      align-items: center;
      gap: 10px;
    }
    .loading-spinner {
      display: inline-block;
      width: 20px;
      height: 20px;
      border: 3px solid rgba(255,255,255,0.3);
      border-radius: 50%;
      border-top-color: white;
      animation: spin 1s ease-in-out infinite;
    }
    @keyframes spin {
      to { transform: rotate(360deg); }
    }
    .action-buttons {
      display: flex;
      gap: 10px;
      flex-wrap: wrap;
      margin-top: 20px;
    }
    .logo {
      text-align: center;
      margin-bottom: 10px;
    }
    .logo-icon {
      font-size: 36px;
      color: #777;
    }
    
    /* Database Viewer Styles */
    .database-view {
      margin-top: 30px;
    }
    .paper-card {
      background: #252525;
      border-radius: 12px;
      box-shadow: 0 5px 15px rgba(0,0,0,0.2);
      margin-bottom: 20px;
      overflow: hidden;
      transition: all 0.3s ease;
      border-left: 5px solid #777;
    }
    .paper-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 8px 25px rgba(0,0,0,0.3);
    }
    .paper-header {
      padding: 20px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      cursor: pointer;
      background: linear-gradient(90deg, #2a2a2a, #252525);
    }
    .paper-title {
      font-size: 18px;
      font-weight: 600;
      color: white;
      display: flex;
      align-items: center;
      gap: 10px;
    }
    .paper-meta {
      font-size: 14px;
      color: #aaa;
      margin-top: 5px;
      display: flex;
      gap: 15px;
    }
    .paper-id {
      background: #333;
      padding: 3px 8px;
      border-radius: 4px;
      font-family: monospace;
      color: #ddd;
    }
    .paper-stats {
      display: flex;
      gap: 15px;
      margin-top: 10px;
    }
    .stat-item {
      background: #333;
      padding: 5px 10px;
      border-radius: 20px;
      font-size: 13px;
      display: flex;
      align-items: center;
      gap: 5px;
      color: #ddd;
    }
    .paper-details {
      padding: 0 20px 20px;
      background: #2a2a2a;
      border-top: 1px solid #444;
    }
    .paper-content {
      background: #1e1e1e;
      padding: 20px;
      border-radius: 8px;
      margin: 15px 0;
      max-height: 300px;
      overflow-y: auto;
      white-space: pre-wrap;
      line-height: 1.6;
      font-family: 'Courier New', monospace;
      border: 1px solid #444;
      color: #ddd;
    }
    .points-list {
      margin: 20px 0;
      padding-left: 20px;
    }
    .points-list li {
      margin-bottom: 10px;
      padding-left: 10px;
      border-left: 3px solid #777;
      color: #ddd;
    }
    .no-papers {
      text-align: center;
      padding: 40px;
      background: #252525;
      border-radius: 12px;
      border: 2px dashed #444;
    }
    .no-papers i {
      font-size: 48px;
      color: #555;
      margin-bottom: 15px;
    }
    .search-bar {
      margin-bottom: 20px;
      display: flex;
      gap: 10px;
    }
    .search-bar input {
      flex: 1;
      padding: 12px 15px;
      border: 2px solid #444;
      border-radius: 8px;
      font-size: 16px;
      background: #252525;
      color: #e0e0e0;
    }
    .toggle-icon {
      transition: transform 0.3s ease;
      color: #aaa;
    }
    .expanded .toggle-icon {
      transform: rotate(180deg);
    }
    
    /* File input specific */
    .file-input-hidden {
      position: absolute;
      width: 0.1px;
      height: 0.1px;
      opacity: 0;
      overflow: hidden;
      z-index: -1;
    }
    
    @media (max-width: 768px) {
      body {
        padding: 10px;
      }
      .app-container {
        padding: 20px;
      }
      .mode-toggle {
        flex-direction: column;
      }
      .mode-toggle button {
        border-radius: 10px !important;
        margin-bottom: 5px;
      }
      .btn {
        width: 100%;
        margin: 5px 0;
      }
      .action-buttons {
        flex-direction: column;
      }
      .paper-header {
        flex-direction: column;
        align-items: flex-start;
      }
      .paper-stats {
        flex-wrap: wrap;
      }
    }
  </style>
</head>
<body>
  <div id="app" class="app-container">
    <div class="logo">
      <i class="fas fa-book-open logo-icon"></i>
    </div>
    <h1>Bytewise Research Assistant</h1>
    <p class="subtitle">Upload, analyze, and discuss research papers with AI-powered insights</p>
    
    <div class="mode-toggle">
      <button @click="setMode('review')" :class="{ active: mode === 'review' }">
        <i class="fas fa-file-upload"></i> Review Mode
      </button>
      <button @click="setMode('write')" :class="{ active: mode === 'write' }">
        <i class="fas fa-edit"></i> Write New
      </button>
      <button @click="setMode('database')" :class="{ active: mode === 'database' }">
        <i class="fas fa-database"></i> Database
      </button>
    </div>

    <!-- Review Mode -->
    <div v-if="mode === 'review'">
      <h2><i class="fas fa-microscope"></i> Upload Research Paper</h2>
      
      <div class="upload-area"
           :class="{ 'dragover': isDragging }"
           @dragover.prevent="handleDragOver"
           @dragleave="isDragging = false"
           @drop.prevent="handleDrop"
           @click="triggerFileInput">
        <input type="file" id="fileInput" ref="fileInput" @change="handleFileSelect" accept=".pdf" class="file-input-hidden">
        <div class="upload-icon">
          <i class="fas fa-cloud-upload-alt"></i>
        </div>
        <p v-if="!file">Drag & drop your PDF here or click to browse</p>
        <div v-else class="file-info">
          <i class="fas fa-file-pdf"></i>
          <div>
            <strong>{{ file.name }}</strong>
            <p>{{ formatFileSize(file.size) }}</p>
          </div>
        </div>
      </div>
      
      <div class="action-buttons">
        <button class="btn" @click="processPaper" :disabled="!file || isProcessing">
          <span v-if="isProcessing" class="loading-spinner"></span>
          <span v-else><i class="fas fa-cogs"></i> Process Paper</span>
        </button>
      </div>
      
      <div v-if="isProcessing" class="progress-bar">
        <div class="progress-fill" :style="{ width: progress + '%' }"></div>
      </div>
      
      <div v-if="statusMessage" :class="['status-message', statusClass]">
        {{ statusMessage }}
      </div>
      
      <div v-if="paperPoints.length" class="results-container">
        <h3><i class="fas fa-list-ul"></i> Generated Points:</h3>
        <div v-for="(point, index) in paperPoints" :key="index" class="point-item">
          {{ point }}
        </div>
      </div>
    </div>

    <!-- Write New Mode -->
    <div v-if="mode === 'write'">
      <h2><i class="fas fa-lightbulb"></i> Research Prompt</h2>
      
      <textarea v-model="prompt" 
                placeholder="Enter your research question..." 
                rows="5"></textarea>
      
      <div class="action-buttons">
        <button class="btn btn-secondary" 
                @click="generatePoints" 
                :disabled="!prompt.trim() || isGenerating">
          <span v-if="isGenerating" class="loading-spinner"></span>
          <span v-else><i class="fas fa-bolt"></i> Generate Research Points</span>
        </button>
      </div>
      
      <div v-if="isGenerating" class="progress-bar">
        <div class="progress-fill" :style="{ width: progress + '%' }"></div>
      </div>
      
      <div v-if="generatedPoints.length" class="results-container">
        <h3><i class="fas fa-star"></i> Generated Points:</h3>
        <div v-for="(point, index) in generatedPoints" :key="index" class="point-item">
          {{ point }}
        </div>
        
        <div class="action-buttons">
          <button class="btn btn-tertiary" 
                  @click="startDiscussion" 
                  :disabled="isDiscussing || !generatedPoints.length">
            <span v-if="isDiscussing" class="loading-spinner"></span>
            <span v-else><i class="fas fa-comments"></i> Start Discussion</span>
          </button>
        </div>
      </div>
      
      <div v-if="discussion" class="discussion-box">
        <h3><i class="fas fa-comment-dots"></i> Discussion:</h3>
        <p>{{ discussion }}</p>
      </div>
    </div>
    
    <!-- Database Mode -->
    <div v-if="mode === 'database'">
      <h2><i class="fas fa-database"></i> Research Database</h2>
      
      <div class="search-bar">
        <input type="text" v-model="searchQuery" placeholder="Search papers...">
        <button class="btn btn-database" @click="loadDatabase">
          <i class="fas fa-sync-alt"></i> Refresh
        </button>
      </div>
      
      <div v-if="loadingDatabase" class="status-message processing">
        <i class="fas fa-sync-alt fa-spin"></i> Loading...
      </div>
      
      <div v-if="databaseError" class="status-message error">
        <i class="fas fa-exclamation-circle"></i> {{ databaseError }}
      </div>
      
      <div v-if="papers.length">
        <div class="paper-card" v-for="paper in filteredPapers" :key="paper.id">
          <div class="paper-header" @click="togglePaperDetails(paper.id)" :class="{ expanded: paper.expanded }">
            <div>
              <div class="paper-title">
                <i class="fas fa-file-pdf"></i> {{ paper.filename || 'Untitled' }}
              </div>
              <div class="paper-meta">
                <span class="paper-id">ID: {{ paper.id.slice(0,8) }}...</span>
                <span>{{ formatBytes(paper.content_length) }}</span>
              </div>
            </div>
            <i class="fas fa-chevron-down toggle-icon"></i>
          </div>
          
          <div v-if="paper.expanded" class="paper-details">
            <div v-if="paper.detailsLoading" class="status-message processing">
              <i class="fas fa-sync-alt fa-spin"></i> Loading details...
            </div>
            
            <div v-if="paper.fullContent">
              <h3><i class="fas fa-align-left"></i> Content Preview</h3>
              <div class="paper-content">
                {{ paper.fullContent.slice(0, 1000) }}
                <p v-if="paper.fullContent.length > 1000">... (truncated)</p>
              </div>
              
              <h3><i class="fas fa-bullseye"></i> Key Points</h3>
              <ul class="points-list">
                <li v-for="(point, index) in paper.points" :key="index">
                  {{ point }}
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
      
      <div v-if="!papers.length && !loadingDatabase" class="no-papers">
        <i class="fas fa-inbox"></i>
        <h3>No Papers in Database</h3>
        <p>Upload research papers in Review Mode to get started</p>
      </div>
    </div>
  </div>

  <script>
    const { createApp, ref, computed, watch } = Vue
    
    createApp({
      setup() {
        const mode = ref('review')
        const file = ref(null)
        const isDragging = ref(false)
        const isProcessing = ref(false)
        const isGenerating = ref(false)
        const isDiscussing = ref(false)
        const progress = ref(0)
        const statusMessage = ref('')
        const paperPoints = ref([])
        const prompt = ref('')
        const generatedPoints = ref([])
        const discussion = ref('')
        const loadingDatabase = ref(false)
        const databaseError = ref('')
        const papers = ref([])
        const searchQuery = ref('')

        const statusClass = computed(() => {
          if (statusMessage.value.includes('success')) return 'success'
          if (statusMessage.value.includes('Error')) return 'error'
          return 'processing'
        })

        const filteredPapers = computed(() => {
          if (!searchQuery.value) return papers.value
          const query = searchQuery.value.toLowerCase()
          return papers.value.filter(paper => 
            (paper.filename || '').toLowerCase().includes(query) ||
            (paper.fullContent || '').toLowerCase().includes(query)
          )
        })

        function setMode(newMode) {
          mode.value = newMode
          if (newMode === 'database') loadDatabase()
        }

        function formatFileSize(bytes) {
          if (!bytes) return '0 Bytes'
          const k = 1024
          const sizes = ['Bytes', 'KB', 'MB', 'GB']
          const i = Math.floor(Math.log(bytes) / Math.log(k))
          return parseFloat((bytes / Math.pow(k, i)).toFixed(2) + ' ' + sizes[i])
        }

        function triggerFileInput() {
          document.getElementById('fileInput').click()
        }

        function handleFileSelect(event) {
          const files = event.target.files
          if (files && files.length) {
            file.value = files[0]
            statusMessage.value = ''
          }
        }

        function handleDragOver(event) {
          isDragging.value = true
          event.preventDefault()
        }

        function handleDrop(event) {
          isDragging.value = false
          const files = event.dataTransfer.files
          if (files.length && files[0].type === 'application/pdf') {
            file.value = files[0]
            statusMessage.value = ''
          } else {
            statusMessage.value = 'Only PDF files are accepted'
          }
          event.preventDefault()
        }

        async function processPaper() {
          if (!file.value) return
          
          isProcessing.value = true
          progress.value = 30
          statusMessage.value = 'Uploading paper...'
          
          try {
            const formData = new FormData()
            formData.append('file', file.value)
            
            const response = await fetch('/upload', {
              method: 'POST',
              body: formData
            })
            
            progress.value = 70
            
            if (!response.ok) {
              const error = await response.text()
              throw new Error(error || 'Upload failed')
            }
            
            const data = await response.json()
            paperPoints.value = data.points || []
            statusMessage.value = 'Paper processed successfully!'
            
            // Reset file input
            file.value = null
            document.getElementById('fileInput').value = ''
            
          } catch (error) {
            statusMessage.value = `Error: ${error.message}`
          } finally {
            progress.value = 100
            setTimeout(() => {
              isProcessing.value = false
              progress.value = 0
            }, 500)
          }
        }

        async function generatePoints() {
          if (!prompt.value.trim()) return
          
          isGenerating.value = true
          progress.value = 30
          generatedPoints.value = []
          discussion.value = ''
          
          try {
            const response = await fetch('/generate', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({ prompt: prompt.value })
            })
            
            progress.value = 70
            
            if (!response.ok) {
              const error = await response.text()
              throw new Error(error || 'Generation failed')
            }
            
            const data = await response.json()
            generatedPoints.value = data.points || []
            
          } catch (error) {
            generatedPoints.value = [`Error: ${error.message}`]
          } finally {
            progress.value = 100
            setTimeout(() => {
              isGenerating.value = false
              progress.value = 0
            }, 500)
          }
        }

        async function startDiscussion() {
          if (!prompt.value.trim() || !generatedPoints.value.length) return
          
          isDiscussing.value = true
          progress.value = 30
          
          try {
            const response = await fetch('/discuss', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({ prompt: prompt.value })
            })
            
            progress.value = 70
            
            if (!response.ok) {
              const error = await response.text()
              throw new Error(error || 'Discussion failed')
            }
            
            const data = await response.json()
            discussion.value = data.discussion || 'No discussion generated'
            
          } catch (error) {
            discussion.value = `Error: ${error.message}`
          } finally {
            progress.value = 100
            setTimeout(() => {
              isDiscussing.value = false
              progress.value = 0
            }, 500)
          }
        }

        async function loadDatabase() {
          loadingDatabase.value = true
          databaseError.value = ''
          
          try {
            const response = await fetch('/database')
            
            if (!response.ok) {
              const error = await response.text()
              throw new Error(error || 'Database load failed')
            }
            
            const data = await response.json()
            papers.value = data.papers.map(paper => ({
              ...paper,
              expanded: false,
              detailsLoading: false,
              detailsError: '',
              fullContent: ''
            }))
            
          } catch (error) {
            databaseError.value = `Error: ${error.message}`
          } finally {
            loadingDatabase.value = false
          }
        }

        async function togglePaperDetails(paperId) {
          const paper = papers.value.find(p => p.id === paperId)
          if (!paper) return
          
          paper.expanded = !paper.expanded
          
          if (paper.expanded && !paper.fullContent) {
            try {
              paper.detailsLoading = true
              paper.detailsError = ''
              
              const response = await fetch(`/database/${paperId}`)
              
              if (!response.ok) {
                throw new Error(await response.text())
              }
              
              const data = await response.json()
              paper.fullContent = data.content
              paper.points = data.points || []
              
            } catch (error) {
              paper.detailsError = `Error: ${error.message}`
            } finally {
              paper.detailsLoading = false
            }
          }
        }

        return {
          mode,
          file,
          isDragging,
          isProcessing,
          isGenerating,
          isDiscussing,
          progress,
          statusMessage,
          paperPoints,
          prompt,
          generatedPoints,
          discussion,
          loadingDatabase,
          databaseError,
          papers,
          filteredPapers,
          searchQuery,
          statusClass,
          setMode,
          formatFileSize,
          triggerFileInput,
          handleFileSelect,
          handleDragOver,
          handleDrop,
          processPaper,
          generatePoints,
          startDiscussion,
          loadDatabase,
          togglePaperDetails
        }
      }
    }).mount('#app')
  </script>
</body>
</html>