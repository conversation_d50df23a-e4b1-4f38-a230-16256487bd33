import os
import uuid
import tempfile
from fastapi import FastAP<PERSON>, File, UploadFile, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import FileResponse, JSONResponse
from mineru_processor import process_pdf_with_mineru
from database import store_paper, get_relevant_papers, init_db
from schemas import GenerateRequest
from config import settings
import logging
import sqlite3
import json
import aiohttp

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(title="Bytewise Research Assistant")

# CORS configuration
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize database
init_db()

# Frontend setup
frontend_path = "D:\\bytewise module prototyping 2\\frontend"
app.mount("/static", StaticFiles(directory=os.path.join(frontend_path, "static")), name="static")
app.mount("/assets", StaticFiles(directory=os.path.join(frontend_path, "assets")), name="assets")

class OllamaClient:
    def __init__(self):
        self.base_url = "http://localhost:11434"
        self.model = "llama3:8b-instruct-q4_K_M"
        self.max_context_tokens = 6144
        self.timeout = aiohttp.ClientTimeout(total=30)

    async def generate(self, prompt: str, max_tokens: int = 500) -> str:
        try:
            async with aiohttp.ClientSession(timeout=self.timeout) as session:
                payload = {
                    "model": self.model,
                    "prompt": prompt[:self.max_context_tokens],
                    "stream": False,
                    "options": {
                        "temperature": 0.7,
                        "num_ctx": self.max_context_tokens,
                        "max_tokens": min(max_tokens, 2000)
                    }
                }
                async with session.post(f"{self.base_url}/api/generate", json=payload) as response:
                    if response.status != 200:
                        error = await response.text()
                        raise Exception(f"Ollama error {response.status}: {error}")
                    data = await response.json()
                    return data.get("response", "").strip()
        except Exception as e:
            logger.error(f"LLM generation failed: {str(e)}")
            return ""

llm = OllamaClient()

@app.post("/upload")
async def upload_file(file: UploadFile = File(...)):
    if file.content_type != "application/pdf":
        raise HTTPException(400, "Only PDF files are allowed")

    try:
        # Create temp file
        pdf_id = str(uuid.uuid4())
        temp_dir = tempfile.mkdtemp()
        pdf_path = os.path.join(temp_dir, f"{pdf_id}.pdf")
        
        # Save uploaded file
        contents = await file.read()
        with open(pdf_path, "wb") as f:
            f.write(contents)
        
        # Process with Mineru
        mineru_output_dir = os.path.join(settings.mineru_output_dir, pdf_id)
        os.makedirs(mineru_output_dir, exist_ok=True)
        text_content = process_pdf_with_mineru(pdf_path, mineru_output_dir)
        
        # Generate points
        prompt = f"""Extract 5 key research points from this paper:
        {text_content[:4000]}
        Format each as '- [point]'"""
        response = await llm.generate(prompt)
        points = [line.strip() for line in response.split('\n') if line.startswith('-')][:5]
        
        if not points:
            points = ["No key points could be extracted automatically"]

        # Store in database
        store_paper(pdf_id, file.filename, text_content, points)
        
        # Cleanup
        os.unlink(pdf_path)
        os.rmdir(temp_dir)
        
        return {"status": "success", "points": points}

    except Exception as e:
        logger.error(f"Upload failed: {str(e)}")
        raise HTTPException(500, str(e))

@app.post("/generate")
async def generate_points(request: GenerateRequest):
    try:
        if not request.prompt.strip():
            return {"points": ["Please enter a valid prompt"]}

        relevant_papers = get_relevant_papers(request.prompt)
        if not relevant_papers:
            return {"points": ["No relevant papers found"]}

        points = []
        for paper in relevant_papers[:3]:  # Limit to 3 papers
            content = paper['content'][:3000]
            prompt = f"""Generate insights about: {request.prompt}
            From this content:
            {content}
            Format as bullet points with '-'"""
            
            response = await llm.generate(prompt, max_tokens=800)
            if response:
                points.extend([line.strip() for line in response.split('\n') if line.startswith('-')][:3])

        return {"points": points[:10] if points else ["No points generated"]}

    except Exception as e:
        logger.error(f"Generation failed: {str(e)}")
        return {"points": ["Error generating points"]}

@app.post("/discuss")
async def discuss(request: GenerateRequest):
    try:
        if not request.prompt.strip():
            return {"discussion": "Please enter a valid prompt"}

        relevant_papers = get_relevant_papers(request.prompt)
        if not relevant_papers:
            return {"discussion": "No relevant papers found"}

        context = "\n\n".join([f"[Source {i+1}]: {p['content'][:1500]}" 
                             for i, p in enumerate(relevant_papers[:2])])
        
        prompt = f"""Discuss: {request.prompt}
        Using these sources:
        {context}
        Cite sources like [Source 1]"""
        
        discussion = await llm.generate(prompt, max_tokens=1200)
        return {"discussion": discussion if discussion else "Error generating discussion"}

    except Exception as e:
        logger.error(f"Discussion failed: {str(e)}")
        return {"discussion": "Error generating discussion"}

@app.get("/database", response_model=dict)
async def view_database():
    try:
        conn = sqlite3.connect(settings.database_path)
        c = conn.cursor()
        c.execute("SELECT id, filename, content, points FROM papers")
        papers = c.fetchall()
        conn.close()
        
        return {
            "papers": [{
                "id": p[0],
                "filename": p[1],
                "content_preview": p[2][:100] + "..." if p[2] else "",
                "content_length": len(p[2]) if p[2] else 0,
                "points": json.loads(p[3]) if p[3] else []
            } for p in papers],
            "total": len(papers)
        }
    except Exception as e:
        logger.error(f"Database error: {str(e)}")
        raise HTTPException(500, "Database error")

@app.get("/")
async def serve_index():
    return FileResponse(os.path.join(frontend_path, "index.html"))

@app.get("/{full_path:path}")
async def serve_spa(full_path: str):
    return FileResponse(os.path.join(frontend_path, "index.html"))

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="127.0.0.1", port=8000)