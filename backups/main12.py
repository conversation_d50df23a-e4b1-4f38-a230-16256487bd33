import os
import uuid
import tempfile
from fastapi import FastAP<PERSON>, File, UploadFile, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import FileResponse, JSONResponse
from mineru_processor import process_pdf_with_mineru
from database import store_paper, get_relevant_papers, init_db
from schemas import GenerateRequest
from config import settings
import logging
import sqlite3
import json
import aiohttp

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(title="Bytewise Research Assistant")

# CORS configuration
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/database")
async def view_database():
    try:
        # 1. Connect to DB with error handling
        conn = None
        try:
            conn = sqlite3.connect(settings.database_path)
            conn.row_factory = sqlite3.Row  # Convert rows to dicts
            
            # 2. Verify table exists
            conn.execute("SELECT 1 FROM papers LIMIT 1")
            
            # 3. Fetch all papers
            papers = conn.execute("""
                SELECT id, filename, content, points 
                FROM papers
            """).fetchall()
            
            # 4. Format response (handle NULL/empty values)
            response = {
                "status": "success",
                "papers": [{
                    "id": p["id"],
                    "filename": p["filename"] or "Untitled",
                    "content_preview": (p["content"][:100] + "...") if p["content"] else "",
                    "points": json.loads(p["points"]) if p["points"] else []
                } for p in papers]
            }
            
            return JSONResponse(response)
            
        except sqlite3.OperationalError as e:
            logger.error(f"Database error: {e}")
            return JSONResponse(
                {"status": "error", "message": "Database not initialized"},
                status_code=404
            )
            
        except Exception as e:
            logger.error(f"Unexpected error: {e}")
            return JSONResponse(
                {"status": "error", "message": "Internal server error"},
                status_code=500
            )
            
    finally:
        if conn: conn.close()

frontend_path = "D:\\bytewise module prototyping 2\\frontend"
app.mount("/app", StaticFiles(directory=frontend_path, html=True), name="frontend")

# Initialize database
init_db()

# Frontend setup

os.makedirs(frontend_path, exist_ok=True)
#app.mount("/static", StaticFiles(directory=os.path.join(frontend_path, "static")), name="static")
#app.mount("/assets", StaticFiles(directory=os.path.join(frontend_path, "assets")), name="assets")

class OllamaClient:
    def __init__(self):
        self.base_url = "http://localhost:11434"
        self.model = "llama3:8b-instruct-q4_K_M"
        self.timeout = aiohttp.ClientTimeout(total=120)
        self.session = None  # Persistent session

    async def ensure_session(self):
        if self.session is None or self.session.closed:
            self.session = aiohttp.ClientSession(timeout=self.timeout)

    async def generate(self, prompt: str, max_tokens: int = 500) -> str:
        try:
            await self.ensure_session()
            payload = {
                "model": self.model,
                "prompt": prompt,
                "stream": False,
                "options": {
                    "temperature": 0.7,
                    "num_ctx": 4096,
                    "max_tokens": max_tokens
                }
            }
            async with self.session.post(
                f"{self.base_url}/api/generate",
                json=payload
            ) as response:
                if response.status != 200:
                    error = await response.text()
                    logger.error(f"Ollama error: {error}")
                    return ""
                return (await response.json()).get("response", "").strip()
        except Exception as e:
            logger.error(f"LLM connection failed: {str(e)}")
            return ""

llm = OllamaClient()

@app.post("/upload")
async def upload_file(file: UploadFile = File(...)):
    if file.content_type != "application/pdf":
        raise HTTPException(400, "Only PDF files are allowed")

    temp_dir = tempfile.mkdtemp()
    pdf_id = str(uuid.uuid4())
    pdf_path = os.path.join(temp_dir, f"{pdf_id}.pdf")
    
    try:
        # Save uploaded file
        with open(pdf_path, "wb") as f:
            f.write(await file.read())

        # Process with Mineru
        output_dir = os.path.join(settings.mineru_output_dir, pdf_id)
        os.makedirs(output_dir, exist_ok=True)
        text_content = process_pdf_with_mineru(pdf_path, output_dir)

        # Generate points with strict formatting
        prompt = f"""Extract exactly 5 key research points from this paper.
        Format each point EXACTLY like this:
        - [concise point here]
        Include nothing else in your response.

        Paper content:
        {text_content[:4000]}"""

        response = await llm.generate(prompt, max_tokens=800)
        
        # Process and clean points
        raw_points = []
        if response:
            raw_points = [
                line.strip() 
                for line in response.split('\n') 
                if line.strip().startswith('-') and len(line.strip()) > 2
            ]
        
        # Ensure we have exactly 5 points
        points = []
        for i in range(5):
            if i < len(raw_points):
                # Clean the point
                point = raw_points[i].strip()
                if point.startswith('- '):
                    point = point[2:]
                points.append(f"- {point}")
            else:
                # Fallback points
                points.append(f"- Key point {i+1} from research")

        # Prepare response
        formatted_points = []
        for point in points:
            formatted_points.append({
                "text": point,
                "source": file.filename,
                "sourceId": pdf_id
            })

        # Store in database (store as JSON string)
        store_paper(pdf_id, file.filename, text_content, json.dumps(points))

        return JSONResponse({
            "status": "success",
            "points": formatted_points,
            "id": pdf_id,
            "filename": file.filename
        })

    except Exception as e:
        logger.error(f"Upload failed: {str(e)}")
        raise HTTPException(500, f"Processing failed: {str(e)}")
    finally:
        # Clean up temp files
        try:
            if os.path.exists(pdf_path):
                os.unlink(pdf_path)
            if os.path.exists(temp_dir):
                os.rmdir(temp_dir)
        except Exception as e:
            logger.warning(f"Temp file cleanup failed: {str(e)}")

@app.get("/download/{paper_id}")
async def download_markdown(paper_id: str):
    try:
        # Check if paper exists in database
        conn = sqlite3.connect(settings.database_path)
        c = conn.cursor()
        c.execute("SELECT filename FROM papers WHERE id = ?", (paper_id,))
        paper = c.fetchone()
        conn.close()
        
        if not paper:
            raise HTTPException(404, "Paper not found")
        
        # Look for markdown file in mineru output directory
        paper_dir = os.path.join(settings.mineru_output_dir, paper_id)
        if not os.path.exists(paper_dir):
            raise HTTPException(404, "Paper directory not found")
        
        # Search for markdown files in the paper directory
        md_files = []
        for root, _, files in os.walk(paper_dir):
            for file in files:
                if file.endswith('.md'):
                    md_files.append(os.path.join(root, file))
        
        if not md_files:
            raise HTTPException(404, "No markdown files found in paper directory")
        
        # Use the first markdown file found (you might want to add more specific logic if needed)
        md_path = md_files[0]
        md_filename = os.path.basename(md_path)
        
        return FileResponse(
            md_path,
            media_type="text/markdown",
            filename=md_filename
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Download failed: {str(e)}")
        raise HTTPException(500, "Failed to download markdown file")

@app.post("/generate")
async def generate_points(request: GenerateRequest):
    try:
        relevant_papers = get_relevant_papers(request.prompt)
        if not relevant_papers:
            return {
                "status": "success",
                "points": [{
                    "text": "No relevant papers found",
                    "source": None,
                    "sourceId": None
                }]
            }

        points = []
        for paper in relevant_papers[:5]:  # Limit to 5 most relevant papers
            prompt = f"""Extract 3-5 key insights about: {request.prompt}
            From this research:
            {paper['content'][:3000]}
            Format each point as:
            - [concise insight]"""
            
            response = await llm.generate(prompt)
            if response:
                points.extend([{
                    "text": line.replace('- ', '').strip(),
                    "source": paper.get('filename', 'Unknown Paper'),
                    "sourceId": paper['id']
                } for line in response.split('\n') if line.strip().startswith('-')][:5])

        return {
            "status": "success",
            "points": points[:10]  # Return max 10 points
        }

    except Exception as e:
        logger.error(f"Generation failed: {str(e)}")
        return {
            "status": "error",
            "message": "Failed to generate points"
        }

@app.post("/discuss")
async def discuss_topic(request: GenerateRequest):
    try:
        relevant_papers = get_relevant_papers(request.prompt)
        if not relevant_papers:
            return JSONResponse({
                "status": "success",
                "discussion": "No relevant papers found to discuss"
            })

        context = "\n\n".join(
            f"Paper {i+1} ({p['filename']}):\n{p['content'][:1000]}..."
            for i, p in enumerate(relevant_papers[:3])
        )

        prompt = f"""Provide a detailed discussion about: {request.prompt}
        Using these research papers as context:
        {context}
        Structure your response with:
        1. Key findings
        2. Comparative analysis
        3. Potential applications
        4. Limitations"""
        
        discussion = await llm.generate(prompt, max_tokens=1500)
        return JSONResponse({
            "status": "success",
            "discussion": discussion if discussion else "Discussion generation failed"
        })
    except Exception as e:
        logger.error(f"Discussion failed: {str(e)}")
        return JSONResponse({
            "status": "error",
            "message": "Discussion generation failed"
        }, status_code=500)

        
@app.get("/database/{paper_id}")
async def view_paper_details(paper_id: str):
    try:
        conn = sqlite3.connect(settings.database_path)
        conn.row_factory = sqlite3.Row
        c = conn.cursor()
        c.execute("SELECT id, filename, content, points FROM papers WHERE id = ?", (paper_id,))
        paper = c.fetchone()
        conn.close()
        
        if not paper:
            raise HTTPException(404, "Paper not found")
            
        points = (json.loads(paper["points"]) 
                 if paper["points"] and paper["points"].startswith('[') 
                 else (paper["points"] if paper["points"] else []))
        
        return {
            "id": paper["id"],
            "filename": paper["filename"],
            "content": paper["content"],
            "points": points
        }
    except Exception as e:
        logger.error(f"Paper details failed: {str(e)}")
        raise HTTPException(500, str(e))

""""
@app.get("/")
async def serve_index():
    return FileResponse(os.path.join(frontend_path, "index.html"))

@app.get("/{full_path:path}")
async def serve_spa(full_path: str):
    return FileResponse(os.path.join(frontend_path, "index.html"))
"""

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="127.0.0.1", port=8000)