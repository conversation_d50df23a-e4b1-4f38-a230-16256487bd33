import os
import uuid
import tempfile
from fastapi import FastAP<PERSON>, File, UploadFile, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import FileResponse, JSONResponse
from mineru_processor import process_pdf_with_mineru
from database import store_paper, get_relevant_papers, init_db
from schemas import GenerateRequest
from config import settings
import logging
import sqlite3
import json
import aiohttp

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(title="Bytewise Research Assistant")

# CORS configuration
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize database
init_db()

# Frontend setup
frontend_path = "D:\\bytewise module prototyping 2\\frontend"
app.mount("/static", StaticFiles(directory=os.path.join(frontend_path, "static")), name="static")
app.mount("/assets", StaticFiles(directory=os.path.join(frontend_path, "assets")), name="assets")

class OllamaClient:
    def __init__(self):
        self.base_url = "http://localhost:11434"
        self.model = "llama3:8b-instruct-q4_K_M"
        self.timeout = aiohttp.ClientTimeout(total=120)  # 2 minute timeout

    async def generate(self, prompt: str, max_tokens: int = 6144) -> str:
        try:
            async with aiohttp.ClientSession(timeout=self.timeout) as session:
                payload = {
                    "model": self.model,
                    "prompt": prompt,
                    "stream": False,
                    "options": {
                        "temperature": 0.7,
                        "num_ctx": 4096,
                        "max_tokens": max_tokens
                    }
                }
                async with session.post(f"{self.base_url}/api/generate", json=payload) as response:
                    if response.status != 200:
                        error = await response.text()
                        logger.error(f"Ollama error: {error}")
                        return ""
                    return (await response.json()).get("response", "").strip()
        except Exception as e:
            logger.error(f"LLM connection failed: {str(e)}")
            return ""

llm = OllamaClient()

@app.post("/upload")
async def upload_file(file: UploadFile = File(...)):
    if file.content_type != "application/pdf":
        raise HTTPException(400, "Only PDF files are allowed")

    try:
        # Create temp file
        pdf_id = str(uuid.uuid4())
        temp_dir = tempfile.mkdtemp()
        pdf_path = os.path.join(temp_dir, f"{pdf_id}.pdf")
        
        # Save uploaded file
        contents = await file.read()
        with open(pdf_path, "wb") as f:
            f.write(contents)
        
        # Process with Mineru
        output_dir = os.path.join(settings.mineru_output_dir, pdf_id)
        os.makedirs(output_dir, exist_ok=True)
        text_content = process_pdf_with_mineru(pdf_path, output_dir)
        
        # Generate points (bullet-proof version)
        prompt = f"""Extract exactly 15 key bullet points from this research paper, each starting with '- ':
        {text_content[:4000]}"""
        
        response = await llm.generate(prompt)
        points = [line.strip() for line in response.split('\n') if line.strip().startswith('- ')][:5]
        points = points if points else ["- Key concepts extraction failed"]
        
        # Store in database
        store_paper(pdf_id, file.filename, text_content, points)
        
        return {"status": "success", "points": points}

    except Exception as e:
        logger.error(f"Upload failed: {str(e)}")
        raise HTTPException(500, str(e))
    finally:
        if 'pdf_path' in locals() and os.path.exists(pdf_path):
            os.unlink(pdf_path)
        if 'temp_dir' in locals() and os.path.exists(temp_dir):
            os.rmdir(temp_dir)

@app.post("/generate")
async def generate_points(request: GenerateRequest):
    try:
        relevant_papers = get_relevant_papers(request.prompt)
        if not relevant_papers:
            return {"points": ["No relevant papers found"]}

        points = []
        for paper in relevant_papers[:100]:  # Limit to 100 papers
            prompt = f"""Generate 10 bullet points about: {request.prompt}
            From this content (each point must start with '-'):
            {paper['content'][:3000]}"""
            
            response = await llm.generate(prompt)
            if response:
                points.extend([line.strip() for line in response.split('\n') 
                             if line.strip().startswith('-')][:10])

        return {"points": points[:10] if points else ["No points generated"]}

    except Exception as e:
        logger.error(f"Generation failed: {str(e)}")
        return {"points": ["Error generating points"]}

@app.post("/discuss")
async def discuss(request: GenerateRequest):
    try:
        relevant_papers = get_relevant_papers(request.prompt)
        if not relevant_papers:
            return {"discussion": "No relevant papers found"}

        context = "\n\n".join(f"[Paper {i+1}]: {p['content'][:1000]}" 
                            for i, p in enumerate(relevant_papers[:100]))
        
        prompt = f"""Discuss this topic: {request.prompt}
        Using these papers:
        {context}
        Provide a detailed analysis with citations like [Paper 1]"""
        
        discussion = await llm.generate(prompt, max_tokens=1000)
        return {"discussion": discussion if discussion else "Discussion generation failed"}

    except Exception as e:
        logger.error(f"Discussion failed: {str(e)}")
        return {"discussion": "Error generating discussion"}

@app.get("/database", response_model=dict)
async def view_database():
    try:
        conn = sqlite3.connect(settings.database_path)
        conn.row_factory = sqlite3.Row
        c = conn.cursor()
        c.execute("SELECT id, filename, content, points FROM papers")
        papers = [dict(row) for row in c.fetchall()]
        conn.close()
        
        return {
            "papers": [{
                "id": p["id"],
                "filename": p["filename"],
                "content_preview": p["content"][:100] + "..." if p["content"] else "",
                "content_length": len(p["content"]) if p["content"] else 0,
                "points": json.loads(p["points"]) if p["points"] else []
            } for p in papers],
            "total": len(papers)
        }
    except Exception as e:
        logger.error(f"Database error: {str(e)}")
        raise HTTPException(500, "Database error")

@app.get("/database/{paper_id}", response_model=dict)
async def view_paper_details(paper_id: str):
    try:
        conn = sqlite3.connect(settings.database_path)
        conn.row_factory = sqlite3.Row
        c = conn.cursor()
        c.execute("SELECT id, filename, content, points FROM papers WHERE id = ?", (paper_id,))
        paper = c.fetchone()
        conn.close()
        
        if not paper:
            raise HTTPException(404, "Paper not found")
            
        return {
            "id": paper["id"],
            "filename": paper["filename"],
            "content": paper["content"],
            "points": json.loads(paper["points"]) if paper["points"] else []
        }
    except Exception as e:
        logger.error(f"Paper details failed: {str(e)}")
        raise HTTPException(500, str(e))

@app.get("/")
async def serve_index():
    return FileResponse(os.path.join(frontend_path, "index.html"))

@app.get("/{full_path:path}")
async def serve_spa(full_path: str):
    return FileResponse(os.path.join(frontend_path, "index.html"))

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="127.0.0.1", port=8000)