@echo off
echo Starting Bytewise Research Assistant...

echo Creating directory structure...
mkdir frontend 2>nul
mkdir backend 2>nul
mkdir frontend\static 2>nul
mkdir frontend\assets 2>nul

echo Moving files to correct locations...
move index.html frontend\ 2>nul
move main.py backend\ 2>nul
move mineru_processor.py backend\ 2>nul
move database.py backend\ 2>nul
move schemas.py backend\ 2>nul
move config.py backend\ 2>nul
move requirements.txt backend\ 2>nul

echo Installing dependencies...
cd backend
pip install -r requirements.txt

echo Starting server...
start "" "http://localhost:8000"
uvicorn main:app --reload --port 8000 --host 0.0.0.0