aiohappyeyeballs==2.6.1
aiohttp==3.12.14
aiosignal==1.4.0
albucore==0.0.24
albumentations==2.0.8
annotated-types==0.7.0
antlr4-python3-runtime==4.9.3
anyio==4.10.0
asttokens==3.0.0
attrs==25.3.0
boto3==1.39.9
botocore==1.39.9
certifi==2025.7.14
cffi==1.17.1
charset-normalizer==3.4.2
click==8.2.1
colorama==0.4.6
coloredlogs==15.0.1
colorlog==6.9.0
contourpy==1.3.2
cryptography==45.0.5
cycler==0.12.1
decorator==5.2.1
dill==0.4.0
distro==1.9.0
doclayout_yolo==0.0.4
exceptiongroup==1.3.0
executing==2.2.0
fast-langdetect==0.2.5
fastapi==0.116.1
fasttext-predict==*******
filelock==3.18.0
flatbuffers==25.2.10
fonttools==4.59.0
frozenlist==1.7.0
fsspec==2025.7.0
ftfy==6.3.1
h11==0.16.0
httpcore==1.0.9
httpx==0.28.1
huggingface-hub==0.33.4
humanfriendly==10.0
idna==3.10
ipython==9.4.0
ipython_pygments_lexers==1.1.1
jedi==0.19.2
Jinja2==3.1.4
jiter==0.10.0
jmespath==1.0.1
joblib==1.5.1
json_repair==0.47.8
kiwisolver==1.4.8
loguru==0.7.3
MarkupSafe==2.1.5
matplotlib==3.10.3
matplotlib-inline==0.1.7
modelscope==1.28.0
mpmath==1.3.0
multidict==6.6.3
networkx==3.3
numpy==2.2.6
omegaconf==2.3.0
onnxruntime==1.22.1
openai==1.97.0
opencv-python==*********
opencv-python-headless==*********
packaging==25.0
pandas==2.3.1
parso==0.8.4
pdfminer.six==20250506
pdftext==0.6.3
pillow==11.3.0
prompt_toolkit==3.0.51
propcache==0.3.2
protobuf==6.31.1
psutil==7.0.0
pure_eval==0.2.3
py-cpuinfo==9.0.0
pyclipper==1.3.0.post6
pycparser==2.22
pydantic==2.11.7
pydantic_core==2.33.2
pydantic-settings==2.10.1
Pygments==2.19.2
pyparsing==3.2.3
pypdf==5.8.0
pypdfium2==4.30.0
pyreadline3==3.5.4
python-dateutil==2.9.0.post0
python-dotenv==1.1.1
python-multipart==0.0.20
pytz==2025.2
PyYAML==6.0.2
rapid-table==1.0.5
regex==2024.11.6
reportlab==4.4.2
requests==2.32.4
robust-downloader==0.0.2
s3transfer==0.13.1
safetensors==0.5.3
scikit-learn==1.7.1
scipy==1.16.0
seaborn==0.13.2
setproctitle==1.3.6
setuptools==80.9.0
sglang==0.4.9.post2
shapely==2.1.1
simsimd==6.5.0
six==1.17.0
sniffio==1.3.1
stack-data==0.6.3
starlette==0.47.2
stringzilla==3.12.5
sympy==1.13.3
thop==0.1.1-2209072238
threadpoolctl==3.6.0
tokenizers==0.21.2
torch==2.7.1
torchaudio==2.7.1
torchvision==0.22.1
tqdm==4.67.1
traitlets==5.14.3
transformers==4.53.2
typing_extensions==4.14.1
typing-inspection==0.4.1
tzdata==2025.2
ultralytics==8.3.168
ultralytics-thop==2.0.14
urllib3==2.5.0
uv==0.8.0
uvicorn==0.35.0
wcwidth==0.2.13
win32_setctime==1.2.0
yarl==1.20.1
