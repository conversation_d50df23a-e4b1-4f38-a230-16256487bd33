# Motion Control of Magnetic Microrobot Using Uniform Magnetic Field

QI ZHANG 1, SHUANG SONG 2, PING HE 3, HENG LI 4, HAO-YANG MI 5, WEI WEI 6, (Senior Member, IEEE), ZUXIN LI 7, XINGZHONG XIONG 8, AND YANGMIN $\mathbf { L } \mathbf { \mathbb { P } } ^ { \mathfrak { g } }$ , (Senior Member, IEEE)

1Guangxi Key Laboratory of Automatic Detecting Technology and Instruments, Guilin University of Electronic Technology, Guilin 541004, Chin   
2Department of Mechanical Engineering and Automation, Harbin Institute of Technology, Shenzhen 518055, China   
3School of Intelligent Systems Science and Engineering (Institute of Physical Internet), Jinan University, Zhuhai 519070, China   
4Department of Building and Real Estate, The Hong Kong Polytechnic University, Hong Kong   
5National Engineering Research Center for Advanced Polymer Processing Technology, Zhengzhou University, Zhengzhou 450000, China   
6School of Computer Science and Engineering, Xi’an University of Technology, Xi’an 710048, China   
7School of Engineering, Huzhou University, Huzhou 313000, China   
8Artificial Intelligence Key Laboratory of Sichuan Province, Sichuan University of Science and Engineering, Zigong 643000, China   
9Department of Industrial and Systems Engineering, The Hong Kong Polytechnic University, Hong Kong

orresponding authors: <PERSON><PERSON> (<EMAIL>) and <PERSON> (<EMAIL>

This work was supported in part by the National Natural Science Foundation of China under Grant 11705122, Grant 61902268, and Grant 61573137, in part by the Project on Promoting Scientific Research Ability of Young and Middle-aged People in Guangxi under Grant 2018KY1095, in part by the Specialized Talents in Guangxi under Grant **********, in part by the Outstanding Youth Backbone Project of Jinan University under Grant 2019QNGG26, in part by the Hong Kong Research Grants Council under Grant BRE/PolyU 152099/18E and Grant PolyU 15204719/18E, in part by the Natural Science Foundation of The Hong Kong Polytechnic University under Grant G-YW3X, in part by the Sichuan Science and Technology Program under Grant 20GJHZ0138, Grant 2019YFSY0045, Grant 2018GZDZX0046, and Grant 2018JY0197, in part by the Key Research and Development Program of Shaanxi Province under Grant 2018ZDXM-GY-036, in part by the Open Foundation of Artificial Intelligence Key Laboratory of Sichuan Province under Grant 2018RZJ01, in part by the Guangxi Natural Science Foundation of China under Grant 2018GXNSFAA138092, in part by the Nature Science Foundation of Sichuan University of Science and Engineering under Grant 2017RCL52, in part by the Zigong Science and Technology Program of China under Grant 2019YYJC03 and Grant 2019YYJC15, and in part by the Shaanxi Key Laboratory of Intelligent Processing for Big Energy Data under Grant IPBED7.

ABSTRACT A microrobot with untethered control in 3D space is a good choice to be applied in the fields of biomedicine with in small and confined workspace. In this paper, an electromagnetic actuation system (EMA) which combined with Helmholtz coil and Maxwell coil for the microrobot 5 DOF locomotion in 3D space is built. The magnetic field analysis of the proposed 3D EMA system was analyzed by finiteelement-method (FEM) with multi-physics COMSOL software. The proposed EMA system can produce magnetic field with different characteristics such as a controllable uniform gradient magnetic field, a rotating magnetic field and a oscillating magnetic field in a three-dimensional space by independently changing the current in each coil. In this paper the 3D motion dynamic equation model of microrobot was established. A novel control method for the gravity compensation for the wireless locomotive microrobot was proposed. The proposed method has the property that the direction of magnetic flux and the locomotion path of the microrobot are independent. Meanwhile, it can achieve the horizontal motions or nearly horizontal motions and overcome the gravity well at the same time. It has been verified by experiments in 3D liquid environment. With the proposed method, the microrobot shows good performance in horizontal motions as well as various motions in the 3D space.

INDEX TERMS Magnetic microrobot, 3D wireless locomotion, electromagnetic actuation system.

# I. INTRODUCTION

In recent years, mechanical system has been widely concerned [1], [2]. Wirelesscontrollable microrobots have great

The associate editor coordinating the review of this manuscript and approving it for publication was Jun $\operatorname { H u } ^ { \mathbb { P } }$

potential application in the field of biomedicine [3]. These untethered, wireless controllable microrobots could lead to breakthroughs in traditional treatment such as minimally invasive therapies and diagnostics [4], [5]. Because the microrobot is very small, it is very difficult to integrate the traditional power source into the microrobot, the magnetic field is applied to realize wireless actuation of the microrobot. Magnetic actuation of the microrobot shows good performance with easy controllability and good response, which makes it a good choice for biomedical applications. It is a good choice for biomedical applications because of its good performance with easy controllability and good response [6]. Several groups have carried out researches on electromagnetic actuation (EMA) of microrobot [7], [8]. The field of robotics has made great progress [9], [10].

Some nonuniform magnetic field methods have been proposed to actuate the microrobot. An EMA system which contains four electromagnetic coils was proposed. A microrobot can be controlled to move in a 2D space with the nonuniform magnetic field generated by the coils [11]. A precise closed-loop control of a microrobot in a 3D space by using a EMA system which consisted of 8 independent electromagnetic coils and vision feedback was achieved [12]. An OctoMag system which can achieve 5-DOF motion control of a microrobot was proposed [13]. It has eight electromagnet coils arranged in a certain relationship to produce complex nonuniform magnetic fields. Based on the nonuniform magnetic fields control method, they developed a smaller version of OctoMag, which is called MiniMag and had the similar functions compared to OctoMag [14].

Another magnetic actuation method is applied with the uniform magnetic field. Helmholtz coil can generate a uniform magnetic field, which can be used to align the robot to desired direction. If the structure of the microrobot is spiral structure, the microrobot can be actuated by Helmholtz coil. If the microrobot is other structure, the microrobot is aligned to the desired direction. while Maxwell coil can produce a uniform gradient magnetic field, which can drive the robot moving the aligned direction. In [15], a 2D EMA system has been proposed and designed. It contains a two-axis Helmholtz coil set and a two-axis Maxwell coils set. In [16], the authors proposed a 3D EMA system which consists of a 3-axis Helmholtz coil set, a fixed Maxwell coil and a rotating Maxwell coil. With gravity compensation control method, a microrobot can be moved in a 3D space and achieve 5DoF movement. However, the complexity of the EMA system has been increased with the rotating mechanism. Therefore, an improved 3D EMA system by using three fixed Helmholtz coils and one fixed Maxwell coil has been proposed in [17]. The proposed system had wider working space and less power consumption compared to the previous 3D EMA system [16]. However, the magnetic flux gradient produced by the proposed system was limited in the direction since there is only one pair of Maxwell coils. A locomotion method that used magnetic resonance imaging (MRI) device to drive a ferromagnetic microrobot was proposed in [7], [18]. With the uniform gradient magnetic field from the MRI, the microrobot can achieve 3DoF movement in liquid environment. The advantage of using MRI is that position of the microrobot can be estimated during the actuation, and therefore navigation can be achieved using a predictive control [19]. However, the microrobot can only be moved in limited directions since that there is no additional coils to produce the desired magnetic field to align the microrobot in arbitrary directions.

In this paper, we design a holonomically-actuated system which can create magnetic forces in any direction with Helmholtz Coils and Maxwell Coils. A 3D EMA system with 3 pairs of Maxwell Coils and 3 pairs of Helmholtz Coils was built to verify the proposed method, which can be seen in Figure 1. It has the property that the direction of magnetic flux and the locomotion path of the microrobot are independent. With the proposed method, the microrobot shows good performance in horizontal motions as well as various motions in the 3D space. Experimental results show that the proposed method has good performance in horizontal motions as well as various motions in the 3D space. Compared with others’ work, the novel control method achieves the horizontal motions or nearly horizontal motions and overcomes the gravity well at the same time.

![](img/f840651108bbf06e97fb0ac2eb0d6c7c80ba0710137c5cf7b51496f162ebfc65.jpg)  
FIGURE 1. Model of the electromagnetic actuation system system.

The organization of this paper is as follows. The magnetic field model will be described in Section II. The motion control of the microrobot in the magnetic field and the proposed gravity compensation control algorithm will be analysed in Section III. The design of the EMA system and magnetic field analysis are discussed in Section IV. Experiments will be carried out in Section V. Finally, a discussion and conclusion will be drawn in Section VI.

# II. MAGNETIC FILED MODELS

The microrobot can be controlled with magnetic torque and magnetic field force under the magnetic field. The magnetic field force and the magnetic torque can control the microrobot under the magnetic field. The force and the torque can be expressed as follows,

$$
\begin{array} { l } { \mathbf { F } = V ( \mathbf { M } \cdot \nabla ) \mathbf { B } } \\ { \mathbf { T } = V \mathbf { M } \times \mathbf { B } } \end{array}
$$

where $\mathbf { F }$ is the magnetic force, $\mathbf { T }$ is the magnetic torque, $\mathbf { B }$ is the magnetic flux, $\mathbf { M }$ is the magnetization of the microrobot and $V$ is the volume of the microrobot. Equation (3) shows the magnetic field models of the Helmholtz coil. Equation (4)

shows the magnetic field models of the Maxwell coil.

$$
\begin{array} { r l } & { \mathbf { B } _ { H } ( x , y , z ) = \displaystyle \left[ \left( \frac { 4 } { 5 } \right) ^ { \frac { 3 } { 2 } } \frac { \mu _ { 0 } N I } { a } ~ 0 ~ 0 \right] ^ { T } } \\ & { \mathbf { B } _ { M } ( x , y , z ) = [ g _ { m } x ~ - 0 . 5 g _ { m } y ~ - 0 . 5 g _ { m } z ] ^ { T } } \end{array}
$$

where $\mu _ { 0 }$ is the permeability of vacuum in free space, $N$ is the turns of the coil, $I$ is the current, and $g _ { m }$ is calculated as follows

$$
g _ { m } = { \frac { 1 6 } { 3 } } \left( { \frac { 3 } { 7 } } \right) ^ { \frac { 5 } { 2 } } { \frac { \mu _ { 0 } N I } { a ^ { 2 } } }
$$

# III. GRAVITY COMPENSATION CONTROL METHOD

Assuming that the microrobot moves in a plane $\pi$ in the 3D space. The $z$ -axis is in the plane $\pi$ and the angle between the $x { - } z$ plane and the $\pi$ plane is $\alpha$ . The angle between the locomotion direction of the microrobot and the positive direction of $z$ axis is $\beta$ . Details can be seen in Figure 2.

![](img/0eb175fc315ec97cf61bf11b1c6916874a8453bdfa5abb44ea4a045240b7582f.jpg)  
FIGURE 2. The previous microrobot locomotion control method in 3D space. The magnetic flux and the locomotion path share the same direction.

The goal is to determine the driven magnetic field if $\alpha$ and $\beta$ are known. The angle between the magnetic flux density $\mathbf { B }$ produced by Helmholtz coils anf the positive direction of $z$ axis is $\theta$ . If we set the magnitude of $\mathbf { B }$ as a constant, we can obtain the desired $\mathbf { B _ { d } }$ as follows:

$$
\left\{ \begin{array} { l l } { B _ { d x } = B \sin \theta \cos \alpha } \\ { B _ { d y } = B \sin \theta \sin \alpha } \\ { B _ { d z } = B \cos \theta } \end{array} \right.
$$

where $B \ = \ | | \mathbf { B } | |$ and $\mathbf { B _ { d } } ~ = ~ ( B _ { d x } , B _ { d y } , B _ { d z } ) ^ { T }$ . The magnitude of $\mathbf { M }$ also keeps constant and $M \ = \ | | \mathbf { M } | |$ . The three

components can be expressed as follows:

$$
\left\{ \begin{array} { l l } { M _ { x } = M \sin \theta \cos \alpha } \\ { M _ { y } = M \sin \theta \sin \alpha } \\ { M _ { z } = M \cos \theta } \end{array} \right.
$$

Since $V$ and magnetization $\mathbf { M }$ of the microrobot can be known in advance, the magnetic force $\mathbf { F }$ can be described as follows

$$
\left\{ \begin{array} { l } { F _ { m x } = M _ { x } V g _ { x } = M \sin { \theta } \cos { \alpha } V g _ { x } } \\ { F _ { m y } = M _ { y } V g _ { y } = M \sin { \theta } \sin { \alpha } V g _ { y } } \\ { F _ { m z } = M _ { z } V g _ { z } = M \cos { \theta } V g _ { z } } \end{array} \right.
$$

where $g _ { x } , g _ { y }$ and $g _ { z }$ are components of magnetic flux gradient. In order to control the microrobot following the desired path, $\mathbf { F }$ should meet the following equation

$$
{ \frac { F _ { m x } } { F _ { m y } } } = \cot \alpha = { \frac { M \sin \theta \cos \alpha V g _ { x } } { M \sin \theta \sin \alpha V g _ { y } } }
$$

From equation (9), it can be seen that $g _ { x } = g _ { y }$ . Therefore we use $g _ { h }$ instead of $g _ { x }$ and $g _ { y }$ . We divide $F _ { m z }$ into two parts, $F _ { m z 0 }$ and $F _ { m z ^ { \prime } }$ because of constant gravity and buoyancy forces in the $z$ direction. $F _ { m z _ { 0 } }$ overcomes the gravity and buoyancy forces and $F _ { m z ^ { \prime } }$ donates the $Z$ component of the driving force. $g _ { z }$ is also divided into two parts, $g _ { z _ { 0 } }$ and $g _ { z ^ { \prime } }$ . The following equations can be derived:

$$
\frac { F _ { m z ^ { \prime } } } { \sqrt { F _ { m x } ^ { 2 } + F _ { m y } ^ { 2 } } } = \cot \beta = \frac { M \cos \theta V g _ { z ^ { \prime } } } { M \sin \theta V g _ { h } } = \cot \theta \frac { g _ { z ^ { \prime } } } { g _ { h } }
$$

where $\rho$ is the density of the microrobot and $\rho _ { f }$ is the liquid. If the locomotion direction and the direction of $\mathbf { B }$ is the same, we set $\theta \ = \ \beta$ . Thus $g _ { z ^ { \prime } } = g _ { h }$ can be derived from equation (10). We can obtain $g _ { z _ { 0 } }$ from equation (11)

$$
g _ { z _ { 0 } } = { \frac { ( \rho - \rho _ { f } ) g } { M \cos \theta } }
$$

If we set suitable $g _ { h }$ according to the experimental environment of the locomotive microrobot, we can obtain the desired $\mathbf { F }$ by equation (8) after the path has been decided. Thus currents of each coils can be estimated and the microrobot can move along the desired path in the 3D ROI. However, if the locomotion direction parameter $\beta$ is equal to $9 0 ^ { \circ }$ or nearly to $9 0 ^ { \circ }$ , which means that the microrobot moves in a horizontal or approximately horizontal plane, $g _ { z 0 }$ will tend to infinity according to equation (12). The EMA system cannot produce infinite magnetic flux gradient, therefore the microrobot is unable to move in the horizontal or approximately horizontal plane using this actuation control method. To solve this problem, we propose a novel control method that keeps the angle $\theta$ formed by $\mathbf { B }$ and the positive direction of $Z$ axis constant. The actuation method is shown in Figure 3. $g _ { z ^ { \prime } }$ in equation (10) can be derived as follows:

$$
g _ { z ^ { \prime } } = { \frac { \cot \beta } { \cot \theta } } g _ { h }
$$

![](img/90fc40ac2625a38a58828e16ed9810d0b9d637ec562b40b7fc6fbd81b45a6899.jpg)  
FIGURE 3. The proposed microrobot locomotion control method in 3D space. The directions of the magnetic flux and the locomotion path are different.

And the equation (12) can be expressed as follows:

$$
g _ { z _ { 0 } } = \frac { ( \rho - \rho _ { f } ) g } { M \cos \theta } \leq \operatorname* { m a x } \{ g _ { z _ { 0 } } \}
$$

We can obtain the maximum value of $\theta$ from equation (14) and set the value of $\theta$ in the eligible range. $g _ { z _ { 0 } }$ can then be determined. Finally, the currents of the six-pair coils can be calculated and the control of the microrobot moving in the 3D ROI can be achieved.

TABLE 1. Main parameters of the EMA system.   

<html><body><table><tr><td rowspan="2">Coil</td><td>Radius</td><td>Coil turns</td></tr><tr><td>a(mm)</td><td>N</td></tr><tr><td>Maxwell X</td><td>76</td><td>289</td></tr><tr><td>Maxwell Y</td><td>120.7</td><td>729</td></tr><tr><td>Maxwell Z</td><td>49.2</td><td>121</td></tr><tr><td>Helmholtz X</td><td>88</td><td>196</td></tr><tr><td>Helmholtz Y</td><td>130</td><td>289</td></tr><tr><td>Helmholtz Z</td><td>54.4</td><td>121</td></tr></table></body></html>

# IV. EMA SYSTEM DESIGN

To achieve the proposed actuation method, the EMA system will be built. We set the working space of the magnetic micro robot as $2 5 \times 2 5 \times 2 5 m m ^ { 3 }$ . The working space center coincides with coordinate origin. Basic parameters of the coils are shown in Table 1. In theoretical calculation, the default radius of each coil is the same. However, in actual machining, the wire is wound in a ring groove layer by layer. It is necessary to reduce the error between theoretical calculation and practical production in order to produce expected uniform magnetic field and uniform gradient magnetic field. In the

EMA magnetic system design, we follow the following three principles. Firstly, in order to improve the uniformity of the magnetic field, the cross section of the coil is square. This ensures that the number of radial layers of the coil is equal to the number of axial layers; Secondly, the radial layer coil radius on the axial coil of the same radial layer can be equivalent to the axial middle position; For coils with different radial layers, the coil radius of adjacent layers differs by the diameter $d$ of one wire. According to equations (15) and (16), we can design the structure of the Helmholtz coil and the Maxwell coil, respectively.

$$
\begin{array} { c } { { \displaystyle \sum _ { i = 0 } ^ { k - 1 } \frac { 1 } { a _ { 0 } + d / 2 + d \times 1 } = \frac { k } { a } } } \\ { { \displaystyle \sum _ { i = 0 } ^ { k - 1 } \frac { 1 } { ( a _ { 0 } + d / 2 + d \times 1 ) ^ { 2 } } = \frac { k } { a ^ { 2 } } } } \end{array}
$$

where $k$ is the number of axial and radial layers, $a$ is the theoretical radius value, $a _ { 0 }$ is the bottom radius of the groove.

In addition to the radius of coils and turns of copper wires, other parameters of the electromagnetic coil structure can be obtained based on the three criteria of the coil system design. The details of the parameters of the coil structure was found in reference [20]. For the whole spatial magnetic field distribution, it is not enough to calculate the flux density along the Helmholtz coil axis and the flux density gradient along the Maxwell coil axis tightly. In order to ensure that the designed Helmholtz coil and Maxwell coil can achieve the desired results, we carry out three-dimensional simulation of the designed coil structure. Figure 1 shows the simulation model of designed EMA system. Using finiteelement-method (FEM) by multi-physics COMSOL software, the magnetic filed values were analyzed to verify the performance of our proposed EMA system.

The magnetic flux density simulation results of the designed Helmholtz coil in the three axes are shown in Figure 4. In the simulation process, we set the current through the coil as 1A. The simulation results of our designed Helmholtz coils show that the magnetic field is almost uniform in the vicinity of position $( 0 , 0 , 0 )$ . When the each Helmholtz coil passes 1A current, the magnetic flux density of $X$ -axis, $Y$ -axis and $Z$ -axis Helmholtz coils are $1 . 7 1 3 \mathrm { m T }$ , $1 . 1 2 6 \mathrm { m T }$ 1.911mT respectively. The results showed that the magnetic field produced by the Helmholtz coils in 3D space is uniform.

The gradient of magnetic flux density simulation results of the designed Maxwell coil in the three axes are shown in Figure 5. In the simulation process, we set the current through the Maxwell coils as 1A. When the each Maxwell coil passes 1A current, the gradient of magnetic flux density in the space near position $( 0 , 0 , 0 )$ produced by our designed Maxwell coils was shown in Table 2. The results showed that the magnetic field produced by the Maxwell coils in 3D space is uniform. According to the simulation results, the working space which is in the center of the coils systen is

![](img/071a9acaae1b179ddb4995a2517a7b4cab95db64c685659d832b7c4f2d7f34e5.jpg)  
FIGURE 4. Magnetic flux density of the designed Helmholtz coil by FEM analysis. (a) X-axis Helmholtz coil, (b) Y-axis Helmholtz coil, (c) Z-axis Helmholtz coil.

![](img/012ee69799d660684ee134e3395eedcf3c1cd7229a40bd072ccbbe61b274bf66.jpg)  
FIGURE 5. Resutls of gradient of the Magnetic flux density of the designed Maxwell coil using FEM analysis. (a) Magnetic gradient of $\pmb { \chi }$ direction in X − Y plane produced by X Maxwell coil, $( b )$ Magnetic gradient of Y direction in $\pmb { \chi } - \pmb { \gamma }$ plane produced by X Maxwell coil, (c) Magnetic gradient of $\pmb { z }$ direction in $\pmb { x } - \pmb { Z }$ plane produced by $\pmb { \chi }$ Maxwell coil, (d ) Magnetic gradient of $\pmb { \chi }$ direction in $\pmb { \chi } - \pmb { \gamma }$ plane produced by Y Maxwell coil, (e) Magnetic gradient of $\pmb { \gamma }$ direction in $\pmb { \chi } - \pmb { \gamma }$ plane produced by Y Maxwell coil, $( \pmb { f } )$ Magnetic gradient of $\pmb { z }$ direction in $\pmb { \chi } - \pmb { Z }$ plane produced by Y Maxwell coil, $\mathbf { \sigma } ( { \pmb g } )$ Magnetic gradient of $\pmb { \chi }$ direction in $\pmb { \chi } - \pmb { Z }$ plane produced by $\pmb { z }$ Maxwell coil, (h) Magnetic gradient of Y direction in $\pmb { \chi } - \pmb { \gamma }$ plane produced by $\pmb { z }$ Maxwell coil, $\bar { ( i ) }$ Magnetic gradient of $\pmb { z }$ direction in $\pmb { \chi } - \pmb { Z }$ plane produced by Z Maxwell coil.

$2 5 \times 2 5 \times 2 5 m m ^ { 3 }$ . In the working space the magnetic filed produced by the Helmholtz coils and the Maxwell coils is uniform at the same time. The values of uniform magnetic flux density and the uniform gradient can then be calculated, and the expression of the $\Lambda _ { B , F } ( \mathbf { M } , \mathbf { p } )$ matrix can be obtained.

![](img/6409827952c271b1f1f38d7258dd0977c04eda27daf5ece7043d474f20964fb2.jpg)  
FIGURE 6. Motions of the microrobot with gravity compensation in the X-Z plane using the first control method.

TABLE 2. Gradient of magnetic flux density produced by Maxwell coils.   

<html><body><table><tr><td rowspan="2">Coil</td><td rowspan="2">Gradient in x direction(T/m)</td><td rowspan="2">Gradient in y direction(T/m)</td><td rowspan="2">Gradient in z direction(T/m)</td></tr><tr><td></td></tr><tr><td>x</td><td>0.0386</td><td>-0.0194</td><td>-0.0193</td></tr><tr><td>Y</td><td>-0.0136</td><td>0.0270</td><td>-0.0135</td></tr><tr><td>Z</td><td>-0.0199</td><td>-0.0199</td><td>0.0399</td></tr></table></body></html>

Based on the above simulation and analysis, the EMA system has been set up, which can be seen in Figure 9. It combines a 3-axis Helmholtz coil set and a 3-axis Maxwell coil set. Current fed on coils are generated with DC motor driving device (AQMD3620NS), which can be controlled with a computer program written by VS2010. Two USB digital microscopes(supereyes B005) are used to record the three-dimensional motion of the magnetic microrobot. The purpose of the magnetic actuation control system is to actuate the microrobot from the current position to the target position. Due to the inertia of the mechanical system itself, it will cause time lag in the transmission of energy and information. At the same time, due to the external noise, it will cause the magnetic microrobot movement deviation. In order to control the stable movement of the magnetic microrobot, the position of the microrobot was obtained by the two cameras. The difference between the expected position information and the actual position information can be used as the input of the control end signal. PID algorithm is used to control the output current of the driving device and realize the control of space magnetic field. In this way, the motion of microrobot can be closed-loop controlled. Figure 10 shows the block diagram of control principle of the magnetic microrobot. $P$ is the actual position and $P d$ is the expected position in the Figure.

# V. EXPERIMENT

For further evaluation of the performance of the proposed control method, experiments have been carried out based on the EMA system. The microrobot is a cylindrical NdFeB (N42) magnet with a $2 \mathrm { m m }$ diameter and $3 \mathrm { m m }$ height. The working space is filled with silicone oil of kinematic viscosity(350cs). The camera views are shown in Figure 11. After the experimental platform has been set up, several experiments have been conducted to further verification of the performance of the proposed control method.

Firstly, in order to verify the proposed two gravity compensation control methods, the 2D microrobot actuation experiments in the $\mathbf { X } { - } \mathbf { Z }$ plane were done. The results of the locomotions of the magnetic microrobot in the x-z plane using the first control method are shown in Figure 6. The magnetic microrobot can be moved in the various desired directions $( 3 0 ^ { \circ } , 4 5 ^ { \circ } , 6 0 ^ { \circ } , 9 0 ^ { \circ } )$ . Using this control method, firstly, the microrobot was aligned to the desired direction by the uniform magnetic produced by the Helmholtz coils. Then, the magnetic microrobot was actuated in the desired direction by the the uniform magnetic flux gradient produced by the Maxwell coils. Using the second improved control method, we only keep the value of $\theta$ constant which means the ratio of $B _ { x }$ and $B _ { z }$ is set to tanθ . The locomotion direction of microrobot $\beta$ depends on the magnetic flux gradients produced by Maxwell coils. The locomotion results are shown in Figure 7.

Secondly, we choose an arbitrary plane in the 3D ROI to verify the 3D motion of the microrobot. We use the improved control method to carry out the gravity compensation actuation of the microrobot since some motions in the horizontal or approximately horizontal plane request huge magnetic flux gradient in the Z direction when using the first control method, while the magnetic flux gradients produced by coils have limitations. We conduct microrobot actuation tests along four planning paths, in which $\alpha$ is $- 1 3 5 ^ { \circ }$ , $1 3 5 ^ { \circ }$ , $4 5 ^ { \circ }$ and $- 4 5 ^ { \circ }$ , respectively. $\beta$ is $3 5 ^ { \circ }$ . Those two parameters determine the locomotion directions of microrobot.

![](img/1bbfd5c4f72144d25f458287775c3b47b14ce445ed5d16d02a682ebbf77efafc.jpg)  
FIGURE 7. Motions of the microrobot with gravity compensation in the X-Z plane using the second control method.

![](img/07ee61e78a77725fc85b577cf928d840c1da0d91a98fb6ac2f4fa05717b1b5f6.jpg)  
FIGURE 8. Motions of the microrobot with gravity compensation in the 3D ROI.

Figure 8 shows the results of the 3D wireless actuation of the microrobot along the different paths. The EMA system we designed is capable of wirelessly actuating the microrobot in the 3D space. The proposed control method is also verified.

![](img/96b01a445b786015d5ad741e7149b5fb56f4a0e77c20301cea4205f1cbf884d4.jpg)  
FIGURE 9. Experimental platform of the EMA system.

![](img/0593e4168441b85a0b4f71c789985d9086e3df81aef37f4060248db19577518a.jpg)  
FIGURE 10. Diagram of visual position feedback control of EMA system.

![](img/52b4a6cae5b56d75c89a0d055668691d3fe0457256d55ccb4a98863e80d18720.jpg)  
FIGURE 11. The camera views when recording the 3D motions of the microrobot.

Lastly, we conduct an experiment to verify the ability to achieve horizontal actuation motions of microrobot by using the second control method. Figure 12 shows the result of a rectangular path motion of the microrobot in the 3D space.

![](img/5d7a2b677edef087abcace680a38d2742cf2cfe1169ce4e0f33b69f75ccaf581.jpg)  
FIGURE 12. Motions of the microrobot when following a rectangular path.

In order to achieve the rectangular path motion in the space, the microrobot needs to accomplish twice the horizontal vertical motions. The result shows that the microrobot can be actuated in a horizontal plane with gravity compensation to achieve the rectangular path motion in the 3D ROI. Since there are some errors between the ideal horizontal motions and the actual experiments while the actual experiment achieves, strictly speaking, the approximately horizontal motions, we can still think that the second method we proposed shows good performance in solving the horizontal motions of wireless actuation microrobot and overcoming the gravity at the same time.

# VI. CONCLUSION

In this paper we built an electromagnetic actuation system (EMA) which combined with Helmholtz coil and Maxwell coil to control the magnetic microrobot. The three-dimensional configuration is imported into the physical field COMSOL software. The magnetic field of the coil is simulated by the finite element method and the relevant mathematical model is verified. The structural parameters of the coil are optimized and the design criteria from the ideal coil model to the actual coil model are proposed. The control algorithm of 3D driving gravity compensation for micro robot is proposed. And the closed-loop control algorithm based on position information feedback was proposed. The proposed control method was demonstrated to be capable of solving difficulties in horizontal or approximately horizontal motions. At last, a variety of experiments were done to verify the performance of proposed system and the control method. The experimental results show that the proposed EMA system can wirelessly actuate the magnetic microrobot in the 3D space along the planning path. The microrobot can be

# actuated in various paths including the horizontal or approximately horizontal motions by using the proposed control method.

REFERENCES   
[1] G. Zhang, P. He, H. Li, H. Liu, X.-Z. Xiong, Z. Wei, W. Wei, and Y. Li, ‘‘An incremental feedback control for uncertain mechanical system,’’ IEEE Access, vol. 8, pp. 20725–20734, 2020.   
[2] G. Zhang, P. He, H. Li, Y. Tang, Z. Li, X.-Z. Xiong, W. Wei, and Y. Li, ‘‘Sliding mode control: An incremental perspective,’’ IEEE Access, vol. 8, pp. 20108–20117, 2020.   
[3] Y.-G. Yue and P. He, ‘‘A comprehensive survey on the reliability of mobile wireless sensor networks: Taxonomy, challenges, and future directions,’’ Inf. Fusion, vol. 44, pp. 188–204, Nov. 2018.   
[4] J. Li, B. Esteban-Fernández de Ávila, W. Gao, L. Zhang, and J. Wang, ‘‘Micro/nanorobots for biomedicine: Delivery, surgery, sensing, and detoxification,’’ Sci. Robot., vol. 2, no. 4, Mar. 2017, Art. no. eaam6431. [5] B. J. Nelson, I. K. Kaliakatsos, and J. J. Abbott, ‘‘Microrobots for minimally invasive medicine,’’ Annu. Rev. Biomed. Eng., vol. 12, no. 1, pp. 55–58, 2010.   
[6] L. Zheng, L.-G. Chen, H.-B. Huang, X.-P. Li, and L.-L. Zhang, ‘‘An overview of magnetic micro-robot systems for biomedical applications,’’ Microsyst. Technol., vol. 22, no. 10, pp. 2371–2387, Oct. 2016. [7] C. Dahmen, K. Belharet, D. Folio, A. Ferreira, and S. Fatikow, ‘‘MRI-based dynamic tracking of an untethered ferromagnetic microcapsule navigating in liquid,’’ Int. J. Optomechtron., vol. 10, no. 2, pp. 73–96, Apr. 2016.   
[8] L. Yang, Y. Zhang, Q. Wang, K.-F. Chan, and L. Zhang, ‘‘Automated control of magnetic spore-based microrobot using fluorescence imaging for targeted delivery with cellular resolution,’’ IEEE Trans. Autom. Sci. Eng., vol. 17, no. 1, pp. 490–501, Jan. 2020.   
[9] J. Tian, C.-C. Wang, P. He, and X.-Z. Xiong, ‘‘Consensus in networked nonholonomic robotic systems,’’ J. Central China Normal Univ. (Natural Sci.), vol. 53, no. 6, pp. 909–914, 2019.   
[10] C.-C. Wang, P. He, H. Li, J.-Y. Tian, K. Wang, and Y. Li, ‘‘Noisetolerance consensus formation control for multi-robotic networks,’’ Trans. Inst. Meas. Control, Mar. 2020, doi: 10.1177/0142331219892115.   
[11] M. Dkhil, M. Kharboutly, A. Bolopion, S. Regnier, and M. Gauthier, ‘‘Closed-loop control of a magnetic particle at the air–liquid interface,’’ IEEE Trans. Autom. Sci. Eng., vol. 14, no. 3, pp. 1387–1399, Jul. 2015.   
[12] I. S. M. Khalil, V. Magdanz, S. Sanchez, O. G. Schmidt, and S. Misra, ‘‘Three-dimensional closed-loop control of self-propelled microjets,’’ Appl. Phys. Lett., vol. 103, no. 17, Oct. 2013, Art. no. 172404.   
[13] M. P. Kummer, J. J. Abbott, B. E. Kratochvil, R. Borer, A. Sengul, and B. J. Nelson, ‘‘OctoMag: An electromagnetic system for 5-DOF wireless micromanipulation,’’ IEEE Trans. Robot., vol. 26, no. 6, pp. 1006–1017, Dec. 2010.   
[14] B. E. Kratochvil, M. P. Kummer, S. Erni, R. Borer, D. R. Frutiger, S. Schurle, and B. J. Nelson, MiniMag: A Hemispherical Electromagnetic System for 5-DOF Wireless Micromanipulation. Berlin, Germany: Springer, 2014.   
[15] H. Choi, J. Choi, G. Jang, J.-O. Park, and S. Park, ‘‘Two-dimensional actuation of a microrobot with a stationary two-pair coil system,’’ Smart Mater. Struct., vol. 18, no. 5, May 2009, Art. no. 055007.   
[16] C. Yu, J. Kim, H. Choi, J. Choi, S. Jeong, K. Cha, J.-O. Park, and S. Park, ‘‘Novel electromagnetic actuation system for three-dimensional locomotion and drilling of intravascular microrobot,’’ Sens. Actuators A, Phys., vol. 161, nos. 1–2, pp. 297–304, Jun. 2010.   
[17] H. Choi, K. Cha, S. Jeong, J.-O. Park, and S. Park, ‘‘3-D locomotive and drilling microrobot using novel stationary EMA system,’’ IEEE/ASME Trans. Mechatronics, vol. 18, no. 3, pp. 1221–1225, Jun. 2013.   
[18] C. Dahmen, B. Zeischke, C. Geldmann, S. Fatikow, and A. Kluge, ‘‘Threedimensional actuation of small ferromagnetic objects in MRI,’’ in Proc. Int. Symp. Optomechatronic Technol. (ISOT ), Oct. 2012, pp. 1–2.   
[19] K. Belharet, D. Folio, and A. Ferreira, ‘‘Three-dimensional controlled motion of a microrobot using magnetic gradients,’’ Adv. Robot., vol. 25, no. 8, pp. 1069–1083, Jan. 2011.   
[20] S. Song, S. Song, and M. Q.-H. Meng, ‘‘Electromagnetic actuation system using stationary six-pair coils for three-dimensional wireless locomotive microrobot,’’ in Proc. IEEE Int. Conf. Inf. Autom. (ICIA), Jul. 2017, pp. 305–310.

QI ZHANG received the B.S. and M.S. degrees from the School of Mechanical Engineering, Nanjing University of Science and Technology, Nanjing, China, in 2002 and 2004, respectively, and the Ph.D. degree from the School of Electronic Information and Electrical Engineering, Shanghai Jiaotong University, Shanghai, China, in 2008.

![](img/fd8bb2f9d940e2bd7660b2bc41e5480376c536ca57c5429caa24b2db3de68794.jpg)

He is currently an Associate Professor with the School of Electronic Engineering and Automation, Guilin University of Electronic Technology,

Guilin, China. His research interests include computer vision and microrobot control.

SHUANG SONG received the B.S. degree in computer science and technology from North Power Electric University, in 2007, the M.S. degree in computer architecture from the Chinese Academy of Sciences, in 2010, and the Ph.D. degree in computer application technology from the University of Chinese Academy of Sciences, China, in 2013.

![](img/5b7f1f22343a0f2f1c94f26e7624318041d878022a62c5366c0e3188d4e9929a.jpg)

He is currently an Assistant Professor with the Department of Mechanical Engineering and

Automation, Harbin Institute of Technology, Shenzhen, Guangdong, China. His main research interests include magnetic tracking and actuation for bioengineering applications, such as surgical robots, micro manipulation, and so on.

![](img/b5b529bc88f91a8ffcf5adf787b821665e77394edd375dcb90681325278ff529.jpg)

PING HE was born in Huilongya, Nanchong, China, in November 1990. He received the B.S. degree in automation from the Sichuan University of Science and Engineering, Zigong, Sichuan, China, in June 2012, the M.S. degree in control science and engineering from Northeastern University, Shenyang, Liaoning, China, in July 2014, and the Ph.D. degree in electromechanical engineering from the Universidade de Macau, Taipa, Macau, in June 2017.

From December 2015 to November 2018, he was an Adjunct Associate Professor with the Department of Automation, Sichuan University of Science and Engineering. From August 2017 to August 2019, he was a Postdoctoral Research Fellow with the Emerging Technologies Institute, The University of Hong Kong, and the Smart Construction Laboratory, The Hong Kong Polytechnic University. Since December 2018, he has been a Full Professor with the School of Intelligent Systems Science and Engineering, Jinan University, Zhuhai, Guangdong, China. He has authored two books and more than 50 articles. His research interests include sensor networks, complex networks, multiagent systems, artificial intelligence, control theory, and control engineering.

Dr. He is a Reviewer Member of Mathematical Reviews of American Mathematical Society (Reviewer Number: 139695). He was a recipient of the Liaoning Province of China Master’s Thesis Award for Excellence, in March 2015, and the IEEE Robotics and Automation Society Finalist of Best Paper Award, in July 2018. He serves as a Section Editor for Automatika: Journal for Control, Measurement, Electronics, Computing and Communications, an Academic Editor for PLOS ONE, and an Associate Editor for Proceedings of the Institution of Mechanical Engineers, Part E: Journal of Process Mechanical Engineering and IET The Journal of Engineering.

![](img/3706d934043ddfd06abc136bdf3fe8893ab4ea0382c15836fa48a644715fe068.jpg)

HENG LI was born in Hunan, China, in 1963. He received the B.S. and M.S. degrees in civil engineering from Tongji University, in 1984 and 1987, respectively, and the Ph.D. degree in architectural science from The University of Sydney, Australia, in 1993.

From 1993 to 1995, he was a Lecturer with James Cook University. From 1996 to 1997, he was a Senior Lecturer with the Civil Engineering Department, Monash University. Since 1997, he has been gradually promoted from an Associate Professor to a Chair Professor of construction informatics with The Hong Kong Polytechnic University. He has authored two books and more than 500 articles. His research interests include building information modeling, robotics, functional materials, and the Internet of Things.

Dr. Li was a recipient of the National Award from Chinese Ministry of Education, in 2015, and the Gold Prize of Geneva Innovation, in 2019. He is a Reviews Editor of Automation in Construction.

![](img/50e37d4c2fa0765750b96c4d36309c9de22fe246ef0c2a60f1901a634c2a748e.jpg)

HAO-YANG MI received the bachelor’s and Ph.D. degrees from the Faculty of Mechanical Engineering, South China University of Technology, Guangzhou, China, in 2010 and 2015, respectively.

From 2016 to 2018, he was a Postdoctoral Researcher with the University of Wisconsin– Madison, USA. From 2018 to 2019, he was a Research Fellow with The Hong Kong Polytechnic University. He is currently an Associate Professor with the National Engineering Research Center for Advanced Polymer Processing Technology, Zhengzhou University. He also directs several projects on artificial intelligence and flexible sensors. He has published nearly $1 0 0 { \mathrm { S C I } }$ journal publications so far, with a total citation more than 2000 and an H-index of 27. He has composed a book chapter. He has applied 17 U.S. and China patents. His current research interests include intelligent materials, artificial intelligence, detection technology, automation device, and flexible sensors. He serves as a regular reviewer for many journals.

![](img/6a8ae09fb84b69b767e8597d6bfb83b9c5777d37dd09fd5da31671fc9d58d8e3.jpg)

WEI WEI (Senior Member, IEEE) received the M.S. and Ph.D. degrees from Xi’an Jiaotong University, Xi’an, China, in 2005 and 2011, respectively. He is currently an Associate Professor with the School of Computer Science and Engineering, Xi’an University of Technology, Xi’an. He ran many funded research projects as a Principal Investigator and a Technical Member. He has published around 100 research articles in international conferences and journals. His current research interests include the areas of wireless networks, wireless sensor networks, image processing, mobile computing, distributed computing, pervasive computing, the Internet of Things, and sensor data clouds. He is a Senior Member of the China Computer Federation (CCF). He is also a TPC member of many conferences and a Regular Reviewer of the IEEE TRANSACTIONS ON PARALLEL AND DISTRIBUTED SYSTEMS, the IEEE TRANSACTIONS ON IMAGE PROCESSING, the IEEE TRANSACTIONS ON MOBILE COMPUTING, the IEEE TRANSACTIONS ON WIRELESS COMMUNICATIONS, and so on. He is also an Editorial Board Member of Future Generation Computer System, IEEE ACCESS, Ad Hoc & Sensor Wireless Sensor Network, Institute of Electronics, Information and Communication Engineers, and KSII Transactions on Internet and Information Systems.

![](img/eb456045b068ba3eb4e81f186d6d646fc73f2ee4404b0d0db4ea8fe20d4d35bd.jpg)

![](img/79a114f0bfd88f5e6b96754f94f02b538f8d6970b7cf74a881d406de11442fbc.jpg)

ZUXIN LI was born in Zhejiang, China, in 1972. He received the B.S. degree in industrial automation from the Zhejiang University of Technology, China, in 1995, the M.S. degree in communication and information system from Yunnan University, China, in 2002, and the Ph.D. degree in control theory and control engineering from the Zhejiang University of Technology, in 2008.

From May 2009 to March 2013, he was a Postdoctoral Research Fellow with the Institute of

Cyber-Systems and Control, Zhejiang University, China. From August to November 2013, he was a Visiting Scholar with Dalhousie University, Canada. He is currently a Full Professor with the School of Engineering, Huzhou University, China. His research interests include networked control systems, robust control, estimation, prognostics, and health management.

XINGZHONG XIONG received the B.S. degree in communication engineering from the Sichuan University of Science and Engineering, Zigong, China, in 1996, and the M.S. and Ph.D. degrees in communication and information system from the University of Electronic Science and Technology of China (UESTC), in 2006 and 2009, respectively. In 2012, he completed a Research Assignment with the Postdoctoral Station of Electronic Science and Technology, UESTC. He is currently a Professor with the School of Automation and Information Engineering, Sichuan University of Science and Engineering. His research interests include wireless and mobile communications technologies, intelligent signal processing, the Internet-of-Things technologies, and very large-scale integration (VLSI) designs.

YANGMIN LI (Senior Member, IEEE) received the B.S. and M.S. degrees in mechanical engineering from Jilin University, Changchun, China, in 1985 and 1988, respectively, and the Ph.D. degree in mechanical engineering from Tianjin University, Tianjin, China, in 1994.

![](img/ee71e5e5922129dfbe546a44c595aa1ade95f856d7f2ad5700f018ccb6c7e6c7.jpg)

He started his academic career, in 1994. He was a Lecturer with the Mechatronics Department, South China University of Technology, Guangzhou, China. From May to November 1996, he was a Fellow with the International Institute for Software Technology, United Nations University (UNU/IIST). He was a Visiting Scholar with the University of Cincinnati, in 1996. He was a Postdoctoral Research Associate with Purdue University, West Lafayette, IN, USA, in 1997. He was an Assistant Professor, from 1997 to 2001, an Associate Professor, from 2001 to 2007, and a Full Professor, from 2007 to 2016, all with the University of Macau. He is currently a Full Professor with the Department of Industrial and Systems Engineering, The Hong Kong Polytechnic University, Hong Kong. He has authored or coauthored more than 400 scientific articles in journals and conferences. His research interests include micro/nanomanipulation, compliant mechanism, precision engineering, robotics, and multibody dynamics and control.

Dr. Li is an Associate Editor of the IEEE TRANSACTIONS ON AUTOMATION SCIENCE AND ENGINEERING, MECHATRIONICS, IEEE ACCESS, and the International Journal of Control, Automation, and Systems.