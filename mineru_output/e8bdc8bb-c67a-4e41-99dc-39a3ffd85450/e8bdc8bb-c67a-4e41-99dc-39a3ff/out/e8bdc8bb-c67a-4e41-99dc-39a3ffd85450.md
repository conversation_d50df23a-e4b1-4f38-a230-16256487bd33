# Medical Micro/Nanorobots in Precision Medicine

<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>\*

Advances in medical robots promise to improve modern medicine and the quality of life. Miniaturization of these robotic platforms has led to numerous applications that leverages precision medicine. In this review, the current trends of medical micro and nanorobotics for therapy, surgery, diagnosis, and medical imaging are discussed. The use of micro and nanorobots in precision medicine still faces technical, regulatory, and market challenges for their widespread use in clinical settings. Nevertheless, recent translations from proof of concept to in vivo studies demonstrate their potential toward precision medicine.

# 1. Introduction

The miniaturization of robotic platforms has the potential for advancing medical treatment and diagnosis of patients. These tiny robotic surgeons could give us access to remote and hard to reach sections of the body and perform diverse medical procedures[1-5] Despite the progress of medical micro/nanorobots in the last decade, one of the unmet needs and significant challenges of this field relies on translating these tools toward widespread clinical use. In this direction, this review aims to illustrate recent trends in micro/nano robotic research, focusing on their use in precision medicine toward clinical transition (Figure 1). In terms of this work, a medical micro/nanorobot is defined as an untethered micro/nanostructure that contains an engine capable of transforming diverse types of energy sources into mechanical force aimed toward performing a medical procedure.[6-10] Although recent reviews have previously covered generalized or specific top. ics in the use of biomedical applications,[11-14] this review aims to bring together a comprehensive and thorough overview of the most recent developments of micro/nanorobots from a precision medicine perspective, highlighting the most promising research opportunities for the next decade that could have a profound impact on human health.

These areas include therapy, surgery, diagnosis, and medical imaging. Each of these areas aims to address different challenges in medicine. For example, motile micro/nanorobots could swim directly into target regions and deliver a precise dosage of a therapeutic payload. Thus, retaining their therapeutic efficacy while reducing side effects, which are a common problem when using a passive delivery approach with low localization efficacy. On the other hand, the use of micro/nanorobots for surgery could reach regions of the body not accessible by catheters or invasive surgery, allowing to sample tissue or deliver therapeutic payloads deep into diseased tissue. The use of tiny robotic surgeons could help reduce invasive surgical procedures, thus reducing patient discomfort and postoperation recovery time.

Micro/nanorobots have potential in medical diagnosis, by isolating pathogens or measuring physical proprieties of tissue in real-time allowing to obtain a precise diagnosis of disease and vital signals. The integration of micro/nanorobots with medical imaging modalities would provide accurate positioning inside the body. These four topics have the potential to benefit human health by offering unique capabilities that their macro-sized counterparts are not able to achieve. After a brief overview of the fundamentals of fabrication and powering of microrobots, each topic will be discussed thoroughly in Sections 2-6. Finally, we outline a roadmap identifying the main challenges and potential risks associated with the translation of medical microrobots from lab to clinic.

# 2. Fundamentals of Micro/Nanorobotics

Researchers in the micro/nanorobotic field commonly refer to the first generation of small-scale robots as "micro/nanomotors" or "micro/nanoengines" defined as small scale structures capable of converting diverse energy sources into locomotion or actuation[15-18] In contrast, a micro/nanorobot is a small scale structure capable of performing a preprogrammed task through mechanical actuation.[6] Given that small scale robotic designs are quite different from their macroscale counterparts, it has been hard to define what should be considered as a micro/nanorobot. When a new research subfield emerges from a well-established field, typically, its progress is judged from what it is not, rather than what it is.[19] For example, when automobiles started to appear on the streets more than a century ago, they were referred to as "horseless carriages."[20] The design of small scale robots face distinct challenges when compared to internal combustion engines, electrical motors, or hydraulic and pneumatic devices. At microscopic scales, viscous forces dominate over inertial forces. Structures at such small scale have to consider environmental effects, such as Brownian motion, caused by random collisions of water molecules with microscale objects that interfere with the directionality of a motile micro/nanorobot.[21]

![](img/32b4c15bbd9b39e8949e85754676812ab75158ab37a1d048096fe3dfc90ef94f.jpg)  
Figure 1. Schmatic of the curent trends of miconnrootics in precision medicine, inclding delivr, surgery dianosis, and medical imagin applications.

# 2.1. Fabrication of Microrobots

At small scales, locomotion is governed by low Reynolds numbers and Brownian motion, thus the primary consideration for designing micro/nanorobots relies on developing engines that are continuously "turn-on" and generate enough force to overcome the drag forces from the environment.[22] Therefore, the design and fabrication of small-scale robots are driven by the need for active materials that can continuously convert diverse energy sources into locomotion. For example, chemically propelled microrobots require the asymmetric distribution of catalytic material to generate directional motion,[23] magnetically propelled micromotors use magnetic materials to induce rotation of a microengineered structures[24] and ultrasound propelled motors employ a structure with density asymmetries to generate pressure gradients that enable their locomotion.[25] The first generation of micro/nanoengines for small scale robotics relied on simple fabrication procedures and geometries.[26] These early nanorobots were fabricated by electrochemically reducing metallic correspondent salts inside nano/micro symmetrical pores.[27] The advantage of this highly explored fabrication method is the large scale production $\scriptstyle \left( > 1 0 0 \ 0 0 0 0 0 \right)$ structures per batch), and the ability to intercalate different electroactive materials (metals, polymers, semiconductors) and designs (hollow tubes, porous wires) in the same construct.28-30] Another bottom-up strategy is self-assembly.[31] This includes layer by layer assembly of sequentially charged materials,[32] generating self-organized polymers to create bowl shape stomatoyces filled in their interior with catalytic materials,[33] and connecting colloids to make engineered structures[34] and magnetic links.[35]

The use of thin-film coatings over templates to generate asymmetric coated structures has also been explored for micro/nanoengine fabrication. For example, Janus micromotors were built by adding a catalytic thin film layer over half of a microsphere using e-beam or sputtering deposition.36-38] On the other hand, atomic layer deposition-based coatings (parylene, titanium oxide, silicon oxide) were used to cover most of the reactive surface except for a small opening, thus reducing the exposed reactive area.39,401 The advantage of these methods is that there are many different types of commercially available microtemplates ranging from polymeric to metallic beads, with more recent examples of the use of biological and bioinspired templates.!41-43]

Other designs with more complex structures, such as microcoils or sophisticated geometries have been built using advanced techniques, including 3D printing,[44 48] glancing angle deposition,[49-52] and rolled-up lithography.[53-55] These novel techniques offer new design capabilities allowing to add functionality by design. However, they are commonly more costly and have limited material selection.

Diverse methods are used to fabricate biohybrid micro/nanorobots.[56,57] For example, self-assembly due to electrostatic interactions between living organisms and synthetic components is used to engineer biohybrid robots. Some microorganisms have either negative or positive charges. Thus, the surface charge of the synthetic component requires to be finely tuned to induce a long-lasting interaction between the synthetic and biological entity. Recent publications have also demonstrated the ability to target binding of the synthetic component with either the head or the tail of a microorganism, based only on noncovalent interactions.58] Another strategy is based on the physical entrapment of functional nanostructures in the rough surface of a microorganism.591 Both strategies are attractive due to their simplicity. However, the lack of covalent interactions between the synthetic component and the microorganism surface makes it prone to detachment under environmental stress.

![](img/bc1a56a6bf1d4a168d4e804a0b7a1669277adedf1c505ad846f69d5277c3eb5f.jpg)  
Figure 2. Powering mechanism for micro/nanorobots. a) Magneticaly propeled micorobot based on rotating microcoil Reproduced with pemissin Coyright 2012, Wile. b Ulund propd miot powred y caviting mouble Rd wth psion4 opy right 2019 . lbe ttr n tsion rust. Recd with psso pight 2015,merica Chal cey.d) Bhybrid mt ba onthe inttif a smwith a synthetic structure. Reproduced with permission.[129] Copyright 2018, American Chemical Society.

The covalent interaction via functionalization of the synthetic component surface with antibodies or chemical agents, which bind to the surface of the motile microorganism, has also been explored. This method ensures long-lasting binding but limits the ability to release the synthetic component on demand.[60,61] Future developments of biohybrids could be assisted by the judicious selection and standardization of the motile microorganism. For example, various microfluidic device technologies containing a periodic array of pores,[62] channels,[63-66] and pillars[67] have been used to select and sort sperm with faster and specific morphology, where these can be used to make better micromotors[68] In general, each fabrication method has unique advantages and challenges illustrated by the tradeoff of large-scale production, material selection, resolution, and freedom of design. Future consideration for fabricating micro/nanorobots should take into consideration the safety of the materials in clinical environments and their fabrication scalability, as most micro/nanorobot research is tailored for laboratory settings.

# 2.2. Robotics Engines at Small Scales

Nature has developed diverse mechanisms to achieve motion at small scales. Many microorganisms possess chemical rotors that enable them to power flagella or cilia, actuated to produce a corkscrew or beading motion leading into locomotion.169-71] Such propulsion mechanism has been the inspiration for rotating synthetic microrobots,[72-74] where synthetic helical microstructures,[75-77] flexible filaments,[78,79] or tumblers[80,81] rotate in axis to that of a bacterial flagellum. Each individual microrobot is energetically independent of the other microrobots rather than been dragged toward the direction given by the magnetic field (Figure 2a).[82-84]

Ultrasound is another external energy input to power microrobots.85-88] The first ultrasound powered micro/nanorobots consisted of metallic asymmetric nanowires powered by a standing wave, in which the generation of localized acoustic streaming stress over the asymmetric nanowire surface produced the driving force for motion.189,90] More recent developments have utilized high intensity focused ultrasound to induce the rapid vaporization of chemical fuels that result in bullet-like motion microrobots[91] and the use of a traveling wave to induce the oscillation of a bubble trapped within a microrobots structure (Figure 2b).[92-94]

Chemical gradients used to power the bacterial flagella have been a source of inspiration for chemically propelled motors. These micromotors use local energy conversion of fuels from the environment into motion.195-98] The propulsion mechanisms included self-electrophoresis,99-101] where an asymmetric fuel decomposition sustains the propulsion of the nanorobot, or bubble propulsion generated by the ejection of gas microbubbles continuously formed inside the catalytic sites of the microrobot engine.1102,103] The first generation of chemically powered micromotors used platinum surfaces as the catalytic engine and peroxide as the fuel. Nevertheless, new fuels and propellant materials have emerged as a biocompatible alternative. For example, enzymes have substituted platinum, allowing them to use a variety of biomolecules, such as glucose or urea as fuel.1104-110] Moreover, biodegradable metals, such as zinc or magnesium have been used as propellants. These can react with the acidic environment of the stomach (Figure 2c).[111] The later materials are of particular interest because the metallic propellant is degraded after use, leaving nontoxic byproducts behind.[112] Optical[113119] and electrical fields[120-125] have also been used to enhance the reaction rate of catalytic materials to produce locomotion, although they are less commonly used in biomedical applications.

Biohybrid robots are built by coupling motile microorganisms (responsible for locomotion), with synthetic structures (to provide additional functionalities) (Figure 2d).[126-131] More recently, advances in synthetic biology have made possible the ability to enhance the capabilities of the motile microorganism without the use of artificial components. For example, genetically engineered bacteria have been programmed to generate diverse active components, such as magnetic particles,[132,133] gas-filled microstructures,[134,135] therapeutic payloads,[136] or responsive probes.[137]

In a nutshell, locally powered microrobots have built-in energy conversion on their active surfaces or exploited the autonomous motility of microorganisms. On the other hand, externally powered microrobots are self-propelled as a result of the interaction between an external field, the robot structure and the media in which they move. Therefore, each powering modality system has a unique advantage. Locally powered micromotors are ideal where autonomy is desired, such as microscale mixing, environmental remediation, and low precision drug delivery. Nevertheless, external power sources are ideal for applications, such as microsurgery or targeted delivery, where control is essential.

# 3. Targeted Delivery

The ability to guide micro/nanorobots directly into diseased tissue could serve as a dynamic platform for the delivery of diverse types of cargoes. For example, pharmaceutical, biologics, living cells, and inorganic therapeutics. Moreover, the stimuli that propels and guides micromotors can be used to improve drug targeting by inducing trigger the release of the therapeutic payload when the micromotor reaches a specific location.[138-141] Micro/nanorobots use diverse methods to carry therapeutic agents, including the use electrostatic or covalent interactions to entrapping them directly on their surface, or by embedding them inside responsive materials. The subsequent release of the therapeutic cargoes is based on diverse mechanisms, including au. tonomous release induced by a change in environment (changes in $\mathrm { p H }$ or temperature) and the use of triggered release induced by application of external fields (near infrared, ultrasound field). In both cases regardless of the loading methodology, either local or external stimuli can result in the change of surface proprieties or degradation of a materials that entails the release of the loaded therapeutic cargo.

# 3.1. Pharmaceutical Drugs

Pharmaceutical drugs consist mostly of small synthetic chemicals designed to treat and prevent diseases. Regardless of the type of administration, the efficacy of drug formulations is often compromised by poor pharmacokinetic proprieties of pharmaceutical drugs, such as short half-life, limited biodistribution, and rapid clearance from the body. Therefore, repeated administrations in high dosages are inevitable to induce the desired therapeutic effect, which could lead to increased toxicity and side effects (e.g., cardiotoxicity).[142,143] In this direction, micro/nanorobots have the potential to overcome this challenge by offering a motile platform capable of delivering a precise dosage in the target area rather than relying on the systemic release of large therapeutic dosages.[144-148]

One of the first examples of micro/nanorobots for delivery of pharmaceuticals was reported a decade ago, where a catalytic nanowire nanorobot was used as a nanoshuttle to pick up, transport, and release doxorubicin/iron oxide loaded poly D,L-lacticco-glycolic acid (PLGA) liposomes. The microrobot contained a nickel segment that served as both a magnetic navigation guide and anchor for PLGA liposomes through weak magnetic interaction. The rapid change of direction resulted in the dislodging of the PLGA liposome due to the increased drag force imposed on the particle.[149] A similar approach was reported using a flexible magnetic microrobot composed of a nickel head and a flexible silver tail.150] The use of nickel/titanium helical microrobots was reported to load calcein loaded liposomes. The vesicles were adsorbed over the $\mathrm { T i O } _ { 2 }$ surfaces of the microrobot via electrostatic interaction.1151] Superparamagnetic microengines arranged in a train-like structure have also been used to capture cells and simultaneously release doxorubicin by diffusion. In this work, the micro/nanorobot was functionalized with tosyl groups on its outer surface, which served to bind cancer cells through their surface and load drugs such as doxorubicin.[152]

Pharmaceutical agents have also been entrapped directly on the surface of micro/nanorobots by using electrostatic interactions. The use of electrostatic forces was reported to load the positively charged brilliant green antiseptic drug into a negatively charged polypyrrole-polystyrene sulfonate segment of an ultrasound propelled nanorobot. The electrostatic interaction was stable at $\mathtt { p H 7 }$ . On the other hand, when the environmental pH became relatively acidic (pH 4) the polypyrrole-polystyrene material segment was protonated, resulting in a triggered release of the loaded Brilliant green drug molecule.[153] In another example, reduced graphene oxide/platinum microrockets were used to transport doxorubicin. The reduced graphene oxide served to load the pharmaceutical drug via $\pi - \pi$ interactions. This method presented a unique trigger-release mechanism based on electrochemical stimuli that disrupt the interactions between the doxorubicin and the graphene surface of the micro/nanomotor.154] Moreover, the use of an electrochemical stimuli as a release mechanism was further expanded by using bismuth coatings to load the therapeutic payload. The injection of electrons into the motor surface caused electrostatic repulsions and the loading of doxorubicin.151] Ultrasound propelled porous nanowire surface was functionalized with an anionic coating that permitted the electrostatic loading of doxorubicin into the micro/nanorobot structure. The porous segment was responsible for increasing the drug loading capacity and for facilitating the release by photothermal effect upon radiation of near-infrared light[156] (Figure 3a). Similarly, mesoporous chemically propelled Janus microrobots were used for near-infrared triggered delivery.[157] The use of pH-sensitive polymers is potentially ideal for autonomous and trigger-release of pharmaceutical drugs next to cancer sites, as the byproducts of cancer cell metabolites result in a local acidic environment.18-160] Another concept for drug delivery relies on inducing drug release based on mechanical rotation.[161,162] A microrobot powered by rotating and alternating currents was used to deliver Nile blue loaded onto the nanorobot via weak electrostatic interactions. The release of this model drug was controlled by modulating the mechanical rotation rate of the nanorobot.[163] Moreover, the use of urea powered nanorobots were reported to enhance doxorubicin release kinetics. A mesoporous silica shell was loaded via electrostatic interactions with a pharmaceutical, doxorubicin, and urease, a biocatalytic enzyme capable of decomposing urea and harnessing the chemical energy into fluid mixing.[164] The limitation of electrostatic loading is that multiple environmental factors can result in dislodging of the loaded pharmaceutical due to the weak binding of the therapeutic cargo with the micro/nanorobot surface.

![](img/d6b9e91d55cfef8483012f1e55772a20df890347a5ec1ca2c113512c1e2f90df.jpg)  
Figure 3. Mico/nanorobot based delivery of pharmacetical. ) Ultrasound proplled nanowires for near-IR trigere delivery of doxorubicin. R proced wh sgh 14   rit fr mach H n d n dee. Reprdc wh piss ht 201 yrd oifamactic tin ms. Reproduced with pmisson.10 Coprigt 2016, Spriger Ntre d Magnticll wer mi wth ezmtic betion for tigered drug relase. Reproduced with permission197] opyright 2018, Wiley.

To increase drug release selectivity, the pharmaceutical compounds have also been directly embedded inside responsive materials to achieve controlled release.165] For example, microrockets were built via layer-by-layer. The assembly consisted of sequential layers of positively charged chitosan and negatively charged sodium alginate, where the therapeutic payload, doxorubicin, was internalized. The microrockets were directed and attached next to HeLa cells and subjected to an ultrasound pulse that locally released the doxorubicin to the cell inducing signs of apoptosis.[166] The same group also reported the use of gelatin-based microrockets[167] and catalase powered layer-bylayer Janus motors,[168] for light-triggered release using nearinfrared of doxorubicin from the bio-gel matrix. Moreover, soft nanorobots were fabricated via self-assembled block copolymers loaded with platinum nanoparticles and doxorubicin. The bilayer structure of the nanomotor could load both hydrophilic and hydrophobic pharmaceuticals.[169] In their follow-up study, they developed a triggered chemical response to glutathione, which cleaved the polyethylene glycol shell, thus releasing the therapeu tic payload.[170]

The use ot magnesium powered microengines has been used toward targeted drug delivery inside the gastrointestinal tract, using biofluids as fuel. Magnesium microengines coated with a poly(N-isopropylacrylamide) have been used to induce temperature-controlled delivery of fluorescein isothiocyanate as a model drug in simulated body fluids or blood plasma.[171] The use of acid-driven micromotors was reported based on Mg coated with a cargo-containing pH-responsive polymer, to neutralize gastric acid in a mouse model. The consumption of the magnesium core by the acidic environment in the gut led to the depletion of hydrogen protons from the gastric environment, thus raising the pH to a neutral environment without the requirement of a proton pump inhibitor. The increase in pH resulted in the degradation of the pH sensitive polymer containing the loaded drug model (Figure 3b).[172] Taking advantage of this principle, further work demonstrated the first in vivo micro/nanorobot therapeutic application by delivering clarithromycin, loaded into a PLGA layer over the magnesium propellant, as a model antibiotic to treat Helicobacter pylori infection inside a mouse stomach.[173] More recently, magnesium microrobots loaded with ampicillin were reported for bacterial treatment.[174] Another animal study used a zinc/iron engine toward gastrointestinal delivery tool of doxorubicin.1175] The use of magnesium-based micromotor was reported to enhance therapeutic efficacy doxorubicin by a synergistic effect on site hydrogen generation.[176] These tiny tools have been integrated into pills[177] and multicompartmentalized segments for delivery with delay actuation release for improving their loading and stability.[178,179]

Biotemplated chemically propelled micro/nanorobots have also been used as carriers for different pharmaceuticals. Platinum-coated plant virus capsules, including brome mosaic virus and cowpea chlorotic mottle virus, were used as nanotemplates for the delivery of tamoxifen, loaded inside their hollow interior. When the virus was internalized inside a cell, its structure denaturalized due to a lower $\mathrm { p H }$ environment, which resulted in the autonomous release of the drug.180] Another type of biotemplate was fabricated by turning red blood cells into microrobots.181,182] This biorobot was fabricated by incorporating quantum dots, doxorubicin, and magnetic nanoparticles into red blood cell micromotors. The fuorescent emission of both quantum dots and doxorubicin provided direct visualization of their loading inside the red blood cell motors at two distinct wavelengths. They demonstrated that these red blood cell micromotors could transport imaging and therapeutic agents at high speed and spatial precision through a complex microchannel network.1183] Pollen structures were used to obtain different microengine proprieties by taking advantage of their resilient outer layer and hollow interior.118,185 More recently, a fully organic microrobot consisting of urease-powered Janus platelet micromotor was engineered by immobilizing enzyme onto the surface of natural platelet cells toward selective adhesion to cancer cells and subsequent delivery of doxorubicin.186] Microorganism-driven micro/nanorobots have also been used as targeted drug delivery systems. Motile sperm loaded with doxorubicin were integrated into a 3D-printed magnetic tubular microstructure that permitted magnetic guidance and assistance to push against the tumor site.[129]

The use of electrostatic self-assembly was used to fabricate biohybrid sperms[187] and Chlamydomonas reinhardtii based microrobots carrying magnetic nanoparticles and therapeutic loads.[188] In another example, Escherichia coli (E. coli) was captured with drug-loaded polyelectrolyte multilayer microparticles containing magnetic nanoparticles, and doxorubicin. This work reported in vitro magnetic guided delivery of doxorubicin encapsulated in the multilayer microparticle toward 4T1 breast cancer cells.189] The use of magnetotactic bacteria was used to carry drug-loaded nanoliposomes (Figure 3c).[190] An external magnetic field was used to guide the biohybrids toward cancer cells.[191] Another advantage of the use of biohybrids as motile carriers relies on their build-in sensing capabilities because microorganisms can detect chemical cues in their environment to find food or avoid danger. Thus, hybrid neutrophil micromotors were loaded with therapeutic cargoes. Neutrophils can detect chemoattractant gradients produced in inflammatory sites and move eliminating pathogens.[192] Doxorubicin-loaded biohybrid sperm have also shown a chemotactic movement to a "sperm activating and attracting factor" molecule. The biohybrid platform was tested in vivo for the targeted delivery of doxorubicin into human ovarian cancer cells.[59] These contributions are of great interest as they use biodegradable materials to transport the therapeutic payloads, leaving nontoxic byproducts behind.

Untethered mobile microrobots have leveraged minimally invasive theragnostic functions precisely and efficiently in hardto-reach, confined, and delicate inner body sites.[193] The use of 3D printed magnetically powered soft biodegradable micro/nanorobot structures with near-infrared triggered tunable doxorubicin delivery were reported. The microstructure was composed of chitosan functionalized photocleavable linkers to load doxorubicin. The tunable application of a near-infrared external field was used to modulate doxorubicin release kinetics. Moreover, lysozyme, a natural enzyme found in the human body, was used to degrade the microrobot structure without producing cytotoxic degradation products.[194] An expansion of this work demonstrated that a gelatin methacryloyl micro/nanorobot could be used to deliver drugs based on polymer swelling.[195] Rolling micro/nanorobots were reported to swim against the bloodstream, and being able to detect target cancer cells using cell-specific antibodies, and induce near infrared triggered release of a doxorubicin payload.[196] The safety tests conducted in the 3D printed GelMa microrobots indicated the limited cytotoxicity after enzymatic degradation (Figure 3d).[197]

The use of metal-organic frameworks (MOF) have been also used for smart therapeutic release based on $\mathsf { p H }$ changes. A zeolitic imidazole framework-8 coated microhelix was able to release model drugs based on changes in the local pH environment.[198] Microrobots composed of multiwalled carbon nanotube coated with $\mathrm { F e } _ { 3 } \mathrm { O } _ { 4 }$ nanoparticles and antiepithelial cell adhesion molecule antibodies were used to target and deliver doxorubicin toward spheroid tumors.[199] Magnetically powered soft robots used the piezoelectric effect to trigger an electrostatic release of doxorubicin.1200] A flexible mesoporous silica nanotube coated with ${ \mathrm { C o F e } } _ { 2 } { \mathrm { O } } _ { 4 }$ magnetic nanoparticles was used as a motile drug carrier. The pores of the structure were covered by G-quadruplexes, consisting of highly ordered DNA structures. These served as valves that opened under magnetic actuation by a conformal change of a rigid G-quadruplex structure and a random single-stranded actuation. Thus, the valve opening released the 6-carboxyfluorescein, model drug.[201]

# 3.2. Biologics

Another relevant area of research has been focused on the micro/nanorobot delivery of biological components, such as proteins, tissue plasminogen activator for thrombolysis, viral vaccines, or antibodies. In contrast to synthetic pharmaceutical drugs, biologics are therapeutic agents that are commonly produced by living systems, including proteins or small segments of a biological component.1202,203] These therapeutics aim to use compounds already produced inside the body as an active agent.

For example, electrically powered rotor nanorobots were used to deliver tumor-necrosis factor. Gold nanowires were functionalized with a hydrophobic layer of 1-dodecanethiol, enabling absorption of tumor-necrosis factor on the surface of the nanorobot. This work demonstrated that a single nanorobot could carry and deliver a threshold of tumor necrosis factor to stimulate the activation of canonical nuclear factor-kappaB transcription factor inside a single cell, simulating immune chain reaction signaling.[204] In another work, ultrasound propelled nanowires used pH-responsive glucose oxidase/phenylboronic acid supramolecular nanovalves to release insulin from a mesoporous silica segment. This approach had a gated responsive release, as insulin only was released in the presence of glucose. The glucose oxidase enzyme catalyzed glucose into gluconic acid, which decreased the local pH and induced protonation of the phenylboronic acid groups opening of the insulin-loaded reservoirs located at the silica segment.205] Chemically propelled microrobots were used to deliver thrombin (coagulant) upstream through the blood vessels toward halt hemorrhage in pig and mouse animal models.|206,2071 The engine of the micro/nanorobot used the chemical degradation of carbonate and tranexamic acid, to generated gas bubbles as a propulsion source (Figure 4a).

The recombinant tissue plasminogen activator (rtPA or tPA), a protein used to break the cross-linked fibrins that provide blood clot structure, has been a highly studied target for micro/nanomotors in the precision medicine tools for delivery. While t-PA is an Food and Drug Administration (FDA) approved biological, there is an important risk of side effects, such as symptomatic intracranial hemorrhages.208] Therefore, this protein requires control of the dosage as well as effective delivery to the blood clot to reduce secondary risks associated with stroke treatment. The first targeted delivery methods for this biological consisted of micro and nanocarriers transporting a small dose of tPA with magnetic nanoparticles coated on a biodegradable polymeric matrix driven to the blood clot via external magnetic fields.209] Taking advantage of the shear stress caused by the constriction in a vessel, a passive diffusion strategy for tPA delivery was developed. Coated tPA-based microaggregates of PLGA nanoparticles, similar in size to natural platelets, were naturally targeted to the blood clot where tPA induced rapid dissolution of the obstruction and allowed normal flow dynamics.[210]An active strategy using rotating nickel-based magnetic nanorobots to improve the local mass transport of t-PA at the blood clot interface has also been reported. The robots were used along with free t-PA to act as an independent input for efficiency. Using a polydimethylsiloxane fluidic channel model to mimic blood vessels, they directed the motor to the clot, produced active motion of the nanorobot, and enhanced the thrombolysis by hydrodynamic convection (Figure 4b).[211] The clinical application of these nickel-based robots was limited because of the toxicity of the material. Therefore, other groups attempted using biocompatible magnetic micromotors, based on superparamagnetic iron oxide $\left( \mathrm { F e } _ { 3 } \mathrm { O } _ { 4 } \right)$ . The tPA was covalently loaded onto the $\mathrm { F e } _ { 3 } \mathrm { O } _ { 4 }$ nanomaterials. Iron oxide nanobots loaded with tPA targeted clots in the brain under magnetic guidance. The application of a rotational magnetic field allowed to perform enough mechanical force to perforate the clot and release the tPA within $3 0 \mathrm { m i n }$ into the clot. This approach enabled the plasminogen to reach new binding sites and enhanced the susceptibility of the clots to lysis.[212] However, a limitation of these ferromagnetic $\mathrm { F e } _ { 3 } \mathrm { O } _ { 4 }$ nanorods t is the tendency of these structures to aggregate. New works have tested the capabilities of porous superparamagnetic $\mathrm { F e } _ { 3 } \mathrm { O } _ { 4 } – \mathrm { C }$ nanorobots that encapsulate tPA to deliver this biological in a target area. After the injection of a solution containing the robots near the brain, they target the blood clot occluded in the middle cerebral artery in the mice. The microrobots were guided by an external magnet, located in proximity to the blood clot. In this case, the clot was dissolved via both tPA (chemical lysis) and rotating nanorobots (mechanical lysis) with the aid of an external rotating magnetic field. More importantly, the microcarriers did not cause liver or kidney damage and could be discharged from the kidney and collected in urine with a magnet or from the biliary system since they were found in bile tracts.[213] More recently, the use of microgel spherical microrobots embedded with aligned magnetic nanoparticles toward thrombolysis by tPA were reported.[214]

Vaccines have been another biological preparation that have been explored for target delivery using micro/nanorobots. To maximize the efficiency of oral vaccines, biomimetic selfpropelling microrobots have been reported to deliver an attenuated vaccine in mice. In a recent paper, they deliver Staphylococcal $\alpha$ -toxin, a hemolytic factor secreted by Staphylococcus aureus. By using magnesium-based microrobots, the vaccine was delivered to the target zone. These microspheres were coated with three more layers: a toxin-inserted red blood cell membrane, chitosan, and pH-responsive enteric polymer layers. The first one was used to detain and neutralize a toxic antigenic payload; the second acted as a mucoadhesive for promoting intestinal localization and the third protected oral drugs from the harsh acidic conditions of the stomach.215] Virus-like nanoparticle bacteriophage $\ Q \beta$ has also been coated on biocompatible Mg-motors for cancer immunotherapy applications in mice (Figure 4c). They were used for peritoneal ovarian tumor treatment. Active delivery in the peritoneal space of ovarian tumors was achieved, and the local distribution and retention of $\ Q \beta$ virus improved versus passive treatment.216] Despite challenges, such as the potential for generating systemic immunity that is still required to overcome using this vaccine, these platforms show the first steps of micro/nanorobots in the delivery of biologicals in vivo and great promise in clinical scenarios.

![](img/585827e27e8d9a193095ea7439560c3d7d03992baf900cf3e33e6c1dcd8680b3.jpg)  
Figure 4. Micro/nanorobot based delivery of biologics and genes. a) ${ \mathsf { C a C O } } _ { 3 }$ powered microrobot for the enhanced delivery of thrombin through flowing blood and  mog R wth p riht 2015, .  Ad ctic rty oie l activator t-PA) medated blo ot dadation by magntilly owere oots. Rdcd th emissn1 right 2014, American hemical Societ. c e  maw crgtdeliver virus cci in musmor modl. Rdth pssi16 yrigt 2020, Wily d)  driven mt for hari deliry capable of gng ginst the fo Rc with p9 oright 2020, American Chemical Society.

Micro/nanorobots have also been implemented in medical tools, such as microneedles to enhance payload delivery. Microneedles have been clinically used to deliver drugs on the epidermis because of its painless effect and localized delivery of target drugs. However, their therapeutic payload distribution is limited by passive diffusion. Recently, magnesium particles have been included as an example of microrobots in degradable microneedle platforms to deliver anti-CTLA-4 antibodies autonomously and actively in a dermal melanoma model in mice. The magnesium engine of the microrobot reacted with the interstitial fuid in the epidermis, leading to a rapid hydrogen production, which generated enough force to open dermal barriers and produce a rapid antibody delivery.[217] In vivo studies using near infrared powered nanorobot reported an increased efficacy urokinase toward thrombus therapy, when compared to passive delivery.[218] Chemically triggered microrobots do not require external stimuli to improve the therapeutic outcome. Therefore, they do not need expensive and bulky external systems commonly used for triggering other active delivery, such as occurring with previously described magnetic micromotors. Sperm driven synthetic horn-like microstructure loaded with heparin, a naturally occurring protein with an anticoagulant function, demonstrated efficient swimming against blood fow (Figure 4d). The biohybrid robot, collectively assembled, achieved efficient locomotion due to the reduction of the drag forces from the fluid. This approach offers unique opportunities to transport anticoagulant upstream too hard to reach regions due to heavy bleeding. Furthermore, sperm have unique potential as an engine of micro/nanorobots because of its limited proliferation or secretion of toxic byproducts and the inhibition of the immune reaction due to its protein-rich surface.[219,220]

# 3.3. Living Cells

Recent developments in the use of micro/nanorobots as cell car. riers offer a unique opportunity for regenerative medicine. The ability to deliver cells directly into the target tissue or stem cell niche could increase their retention rate and survival. Addition. ally, it could help address some of the significant challenges of regenerative cell transplantation.[221] Taking advantage of the large loading capacity of micro/nanorobots, they can be engineered with various types of cells and possess different biological features. One strategy consists of using their microrobot surface as a cell culture scaffold, serving as a mechanical support for the cells to grow over a motile structure. SU-8 microcages, coated with nickel for magnetic response and titanium for compatibility, were used to grow human embryonic kidney 293 cells and subsequently controlled by external magnetic fields.[222] Microrobotic cell scaffolds made of programable materials were used for inducing biochemical and biophysical cues that reg. ulated cell fate before and during their targeted delivery. The motile scaffold consisted of a hollow magnetic cylinder wrapped by a double helix. The inner cavity walls comprised of diverse niche components, including collagen, hyaluronan heparin, and fibronectin, entrapped in a gelatin matrix. This inner wall served to increase the adhesive stability of a transported cell, protect it from undergoing differentiation during transport, and directing it toward the desired lineage. The stem cell loaded microrobot could move along predetermined trajectories under external rotating magnetic fields (Figure 5a). Moreover, the microrobot scaffold demonstrated the ability to induce preosteogenic differentiation of transported stem cells by including bone morphogenetic protein-2 embedded inside the inner matrix.1223] Larger functionalized hydrogel-based robots have been used as cell carriers with the advantage of generating assemblies of different types of cells.22,225] Our group has also expanded this concept by developing ultrasound-based fabrication of highly packed cells embedded in hydrogel ring shape microstructures, which present a promising future for developing fully degradable microrobot scaffolds for the transport of multiple cells into a single structure.[226]

![](img/85837d29ab5d66b35c1fb624f68f4fe29b3df18d12f1fd219e1047c6717489cd.jpg)  
Figure .  rs as  or l tranrt  rer th rmbe ur t ifeitioued wth ] t 2019 We. ) r d mihelix ot for brideir. R h s2 h 2019, AAAS.c) Adiposederive issue loaded micorobot for knee cartilage reeneration. Reroduced with permission.31 opyright 2020, AAAS.

In another case, biodegradable microrobot scaffolds fabricated by 3d printing, were used toward targeted neuronal cell delivery for neurodegenerative disease therapy. The microrobot hydrogel chassis served as a scaffold to support neuronal cell growth and was enzymatically degraded after finishing the cell delivery. The microrobots were embedded with magnetoelectric nanoparticles, composed of a ${ \mathrm { C o F e } } _ { 2 } { \mathrm { O } } _ { 4 }$ core and a $\mathrm { B i F e O } _ { 3 }$ shell. The magnetoelectric nanoparticles served to induce motile magnetic actuation under low magnitude rotating magnetic fields. Moreover, the nanoparticles generated a magnetostrictive transient charged surface that was used to stimulate the differentiation of SH-SY5Y, a neuroblastoma cell line.227] Degradable helical microrobots fabricated using a microfluidic chip also demonstrated the scalable fabrication of NIH 3T3 loaded structures that could stack forming larger cell-loaded aggregates.[228]

Microrobots have already been tested using in vivo models to evaluate target stem cell delivery. Burr-like magnetically propelled spherical microrobots were used for carrying and delivering cells in different animal models, including transport of MC3T3-E1 cells into the yolk of a zebrafish embryo and the delivery of GFP in HeLa cells located inside the left dorsum of the nude mice. In the latter case, the fluorescence intensity increased after 4 weeks, indicating the successful proliferation of the delivered cells.[229] Microrobots delivered hippocampal neural stem cells capable of inducing their proliferation and differentiation into astrocytes, oligodendrocytes, and neurons. They demonstrated that the micromotors could transport colorectal carcinoma cancer cells to tumor microtissue in a liver-tumor micro organ-on-chip in vitro. The nanorobots could also navigate through the mesenchymal stem cells in a nude mouse brain intraperitoneal cavity and rat brain blood vessel in vivo (Fig. ure 5b).[230]

Microrobots were also used for human adipose-derived mesenchymal stem cells for knee cartilage regeneration using an in vivo rabbit model (Figure 5c). The microrobot scaffold consisted of a spherical PLGA microstructure decorated with ferumoxytol magnetic nanoparticles. After mesenchymal stem cells were loaded and grew over the surface of the microrobot, they were injected in the wounded knee of the rabbit. The application of an oscillating magnetic field served to enhance the motion of the microrobot to fill and accumulate at the target site (defects in the knee). Once in there, a permanent magnet was used to hold in place the cell loaded microrobots facilitating the adhesion and proliferation of the stem cells in the target location.[231]

Microrobots have also been used to transport individual cells without functioning as a scaffold, but rather by using chemical interactions or physical stimulation. Different types of blood cells have been coupled with motile robots to take advantage of their biological function. For example, magnesium-based biohybrid micromotors system were integrated with live macrophage cells. This system included the biocompatible propulsion of the magnesium core engine from the microrobot and the biological functions of the microphage, which produced endotoxin neutralization (Figure 6a).[232] E. coli biohybrid microrobots were used to transport a live red blood cell (erythrocyte) via a biotin-avidinbiotin functionalization (Figure 6b). As both sections of the biohybrid robot were "soft," they could maintain the interaction, after passing through a microfluidic channel smaller than their size.[233] In this case, the red blood cell was used as a carrier, although it has the potential to be used as a sponge to capture toxins from blood samples.[234]

Magnetically powered microrobots have been proposed as carries of sperm. The microrobot consisted of microhelice structures that can capture and transport sperm cells inside their hollow interior (Figure 6c).[235,236] Micro/nanorobots have been also used to manipulate and transport oocytes/zygotes. For instance, microrobotic arms reported the ability to surgically remove the nucleus from an oocyte.[237] In a similar work, the microrobotic arms were assisted with an acoustic levitation system to enable a higher degree of precise 3D manipulation of a single oocyte.[238] Spiral-shaped magnetically powered micromotor were used to transport and release individual fertilized oocytes in a microfluidic chip.[239] Such examples demonstrate the potential of micro/nanorobots in assisted fertilization protocols.

# 3.4. Inorganic Agents

Apart from pharmaceuticals, biologics, and cells, inorganic materials offer also hold promise for therapeutic purposes. Their applications include the use of responsive materials that are externally actuated, or the use of metallic ions to treat diseases. The use of sensitive materials that can be activated on-demand in targeted locations aims to reduce the side effects and dosage requirements of pharmaceuticals while maintaining their therapeutic efficacy. Magnetically propelled microrobots have taken advantage of the capacity of a magnetic material to convert kinetic energy produced by an external magnetic field into thermal energy. The localized heat generated at the target area can be translated in energy to kill cells and have a synergistic effect in combination with medication. Park et al. reported a degradable microrobot capable of hyperthermia, with an increase of the local temperature above $4 0 ~ ^ { \circ } \mathrm { C }$ (Figure 7a). This heat triggered the release of the anticancer drug 5-fuorouracil and actuating the $\mathrm { F e } _ { 3 } \mathrm { O } _ { 4 }$ nanoparticles embedded in the robot polymeric matrix.[240] Magnetically triggered release of fluorouracil was previously rested using an animal model.[241] Moreover, microrobot assisted hyperthermia was recently applied to remove plaques in clogs from a simulated blood vessel. This robot consisted of magnetically coated carbon nanotube that was capable of both chemical and magnetic propulsion.[242]

Photothermal microrobots were used to seal the superficial wound in a bleeding animal (Figure 7b). The microrobots, consisting of a thermophoretic Janus microstructure with a gold cap, magnetite nanoparticles, and dyes for infrared laser-assisted tissue welding, were inserted inside an open wound. Upon application of a laser pulse directly into the wound, the micromotor generated a localized temperature increase that denaturalized nearby collagen serving as a "glue" to close the wound. The microrobot was the platform to assist in wound healing, where the pharmaceutical/coagulant was not able to reach the necessary concentrations due to profuse bleeding or lack of localization.[243] More recently, magnetic microrobots consisting of magnetized fixed spir ulina, helical algae microorganism, demonstrated the targeted chemo-photothermal therapy triggered by near-infrared irradiation reaching temperatures above $5 0 ~ ^ { \circ } \mathrm { C }$ (Figure 7c).[244] Later studies reported photothermal therapy against a mouse subcutaneous drug-resistant Klebsiella pneumonia infection model.[245]

Despite considerable progress in reducing hunger in the world, more than one-third of the worldwide population still suffers severe nutritional deficiencies, such as anemia commonly caused due to iron deficiency. Thus, there is a need to deliver minerals and ions in the body, as they are hard to absorb due to their low bioavailability and potential adverse side effects.[246] The use of magnesium powered microrobots powered by gastric fluid was explored for effective delivery of active minerals, including a cocktail delivery of iron and selenium (Figure 7d). Animal experiments demonstrated the ability of the micromotor to enhance mineral absorption in an anemic mice model. The microrobot-based therapy resulted in the normalization of diverse, relevant health parameters, including red blood cell count, and hemoglobin.[247] In a similar direction, microrobots have also been described as active anion delivery systems. These robots were coated with a polymeric viologen, which can selectively in teract with anionic species of different sizes and charge densities. The release of the charge species was achieved by different methods, including electrochemical/photochemical reduction or the presence of an acidic environment.[248]

![](img/6bd781e29cd5df83a6c8137dea9371bb48e327d8342237d87c7b79f7ac75305d.jpg)  
Figue 6. t a crrr f inidal cls  Chly w otrnpting mcrophg achd viattatic ecti Rerth pi201 e.  Brd mrin oll h p ig 2018 A. Matcll  mhix trating s cell. c wth pes] rigt 2016 mercaChal ociet

Moreover, silver ions have been used in combination with microrobots as bactericidal agents, allowing to deliver silver ions into the bacterial cytoplasm resulting in disruption of their outer membrane without the use of pharmaceuticals.[249] Thus, chemically propelled microrobots composed of zeolites have taken advantage of their high absorption capacity to serve as silver ion carriers. The autonomous locomotion of the zeolite microrobot enhanced the interaction between the silver ions and pathogens and increased fluid mixing, resulting in enhanced pathogen killing capabilities.250] Water-based magnesium micromotors decorated with silver nanoparticles were used toward disinfection and removal of E. coli from contaminated samples. The micromotor also contained an inner iron layer that allowed for magnetic recollection and removal of the captured bacteria, leaving the solution without any biological contaminant.251] Magnetically propelled silver-coated nanocoils were also employed as bacterial killing microrobots by direct contact of the bacteria wall with the active surface of the nanocoil. Efficient decontamination of both Gram-negative (E. coli) and Gram-positive (S. aureus) pathogen stains was demonstrated.[252] Nevertheless, the application of microrobots to deliver silver ions has a significant limitation. Most pathogens grow and proliferate inside the mucous wall of the gastrointestinal tract, making a challenge for the silver ions to reach their target. In this direction, an onion inspired multifunctional microrobot was developed as an attracting pathogenic trap for collection and lysis of pathogens (E. coli) in solution. The microrobot released a chemoattractant which permitted the dynamic approximation of motile pathogens to the microrobot, and the subse quent release of silver which destroyed the motile pathogen.[253]

![](img/ab3e87247627e9cb568e837619ac7becc84b49452de9154d0fe9de39780646dd.jpg)  
Figure 7. Miont bad deiry f inc agts.  Magtill roelled mt for hpetha magntic  thrapy. Rproduced with pmson20 Copyright 2019, Wily. b Phthmal miromotr bas wound healing. Rpoced wth pemissn3] pyright 2016, Wile. c) Maeticll proeed no for chm-phhmal hrap. Rdd with pess24] right 019, American Chemical Societ. d) Chmicall proelled mirtor for mnerldelivery. Rdced wth peissio4] pyright 2019, American Chmical ociet.

Although scientists have made great demonstrations of micro/nanomotor in targeted delivery, more work on the design aspects toward improving the selectivity and specificity, navigation in biologically relevant environments, controlled cargo release, real-time-tracking, and feedback should be conducted for successful in vivo application. All these platforms will foresee a great potential because they could come as a valuable tool for cancer therapy, regenerative medicine, and life extension therapy.

# 4. Surgery

Large-scale surgical tools do not have analogous micro/nanoscale counterparts, hindering the ability to operate at this small scale and resulting in minimal tissue penetration. Miniaturization of surgical tools could provide distinct advantages due to their small size and ability to access places where catheters and blades cannot. Micro/nanorobotics could serve as surgical tools, aiming to penetrate or retrieve cellular tissues directly. These untethered minimally invasive systems would provide access to regions of the body that their large-scale robotics counterparts are not able to reach. Besides, they will have the potential to reduce the risk of infection and recovery time.[254] Indeed, micro/nanorobot could complement current surgical robotics tools, aiming to increase the precision and control of human surgeons.

![](img/0b113201ce982a8c0a0665238c3ccb05c06d89fba7b6523066a7e169e60a91e2.jpg)  
Figure 8. Micoot basd biosy and sampling ) Str-shaped griper ollctin tise Rdced with pmson257 Copyright 2015, merican Chemicl Scit. b tr gperclcting re bo cell. Rd wth pissn9 right 2014, Wil.  Mtile mira collecting pathogens. Reproduced with permission.[253]Copyright 2020, Wiley.

# 4.1. Biopsy/Sample Collection

Researchers have illustrated micro/nanorobotic devices that collect tissue samples and bacteria for reduction of damage to tissue, via lowering invasive surgery, and advancing diagnosis. Most untethered microscale robotic devices are still in the centimeter to millimeter range. This size range permits to integrate builtin communication electronics into the motile robotic pills.[255,256] However, further miniaturization from these devices would allow sampling even smaller regions. For instance, star-shaped grippers that are capable of responding to diverse environmental stimuli to close down and capture tissue have been reported (Figure 8a).[257] These tiny medical devices have been evaluated using animal models, demonstrating the ability to excise tissue from a pig bile duct.[58] Biocompatible designs made of responsive hydrogels embedded with magnetic alginate microbeads were magnetically guided and presented infrared light-induced gripping (Figure 8b).[259-262]

Micro/nanorobotics were also explored to collect bacteria inside the body. The use of motile robotic collectors could help expand the understanding of the biome. Deployable microtraps were used for sequestering motile bacteria from a liquid environment. The device consisted of microengineered funnels that confine bacteria into subdivision trap chambers.[263] More recently, motile microtraps, consisting of an onion inspired multilayer structure, were used to collect motile pathogens. The depletion of the magnesium engine core resulted in a hollow structure that served as a structural trap. Moreover, the inner layer released a chemoattractant (serine) that served to attract nearby motile microorganisms and capture E. coli within the microtrap structure (Figure 8c).[253] These examples demonstrate the potential of microrobotic devices for biopsy and sample collection. However, the main challenge that these applications face is the ability to preserve the specimen and avoid contamination. Moreover, motile micro/nanorobots could be used to collect diverse biomarkers, such as exosomes[264-266] (small vesicles excreted from cells) and disease markers.[267,268]

# 4.2. Tissue Penetration

Robotic systems are useful tools to access deep tissue region that are not reachable trough blood vessel absorption. For example, robotic devices in the centimeter range have been widely used for gastrointestinal assisted delivery, where the robot is used to pierce tissue or collect samples.269,270] The miniaturization of robotic devices could enhance our ability to access even more remote locations inside the body. Most of the literature on tissue penetration is composed of externally powered micro/nanorobots, as external fields are capable of penetrating thick biological tissue. The external energy allows continuous operation in diverse environments and a high degree of control. Mag. netic nano/microrobots have been applied to penetrate through the brain of a mouse cadaver. The microdriller robots were inserted intranasally into a mouse brain and propelled under different types of motion, controlled by tuning the applied mag. netic field. The rotational motion of the microdrillers presented enhanced penetration when compared to the use of a linear magnetic gradient.[271] Moreover, this work was expanded to demonstrate the ability to induce behavioral changes in small mammals by activating magnetic microrobots inserted into neural tissue. The application of a low magnitude external field resulted in an increased level of chewing behavior when compared to control experiments.272] These works illustrate the potential of using mechanical stimulation of neural tissue via unthreaded micro/nanorobots.

Additionally, rotating microrobot drillers, powered by an external rotational magnetic field, have demonstrated the ability to penetrate deep inside diverse organs. Tubular microdrillers with sharp ends were used to demonstrate proof of surgical applicability (Figure 9b). The microdriller was power by an external magnetic field, enabling to perform different types of motion by modulating the applied frequency, leading to horizontal or vertical orientation, while the structure rotates in axis. The microdriller was forced to penetrate porcine liver tissue while in a vertical position held in place due to the interaction with the rough tissue surface. The microdriller reached a penetration depth of $2 5 ~ { \mu \mathrm { m } }$ after operation for $1 0 \ \mathrm { m i n }$ . The microstructure was recovered using a permanent magnet leaving behind the drilled hole in the liver tissue.[273] The resulting hollow cavity could be used to seed cells or implant sustain drug release patches. Enzymatically active biomimetic micropropellers were used for the penetration of mucin gels (Figure 9b). This work aimed to solve the limitation of accessing pathogens located at the mucus layer by using a magnetic microdriller functionalized with the enzyme urease in its outer surface. The external catalytic surface reacted with the mucus layer, locally increasing the pH level, resulting in the liquefication of the mucous, thus allowing the micro/nanorobot to propel through the biopolymeric layer.[274]

![](img/2d8f533a94ad35be81a72a306cdab46f7800a387771b782229c4666bab1b1bdf.jpg)  
Figure 9.M fr tie rtin.  Matic dre nalizg int livr ie c th ssio3] right 2013, The Royal ociet of Chemistr. b) Magnetic micorillerpetrating mucin gel. Reproduced with pemssion.274] Copyright 201, AAS. c) Magnetic micodrillr mobing inside the ey. Reduced with peissio7] oright 018 s. asound pwered miobullt fo issue penetration and cleaving. Reproduced with permission.[91] Copyright 2012, Wiley.

Magnetic powered microrobots have also been used to navigate inside the eye, presenting a high degree of control and biocompatibility. The micro/nanorobots were injected into the vitreous cavity through a surgical opening of the eye. A magnetic coil system was used to navigate the micro/nanorobot through the posterior segment of a rabbit eye.[275] The biocompatibility of micro/nanorobot was enhanced by using a polypyrrole coating, presenting minimal inflammatory response in comparison to the not coated counterpart.[276] More recently, submicrometer magnetically propelled robots coated in a liquid perfluorocarbon protective layer were used to minimize biofouling and interaction with biopolymeric networks found inside the vitreous body of an eye. The microrobot presented efficient locomotion inside the porcine eye, being able to navigate freely through its structure (Figure 9c). This study presented evidence that robots smaller than $5 0 0 \mathrm { n m }$ present unobstructed motion inside the eye as they are smaller in size than the mesh size of the biopolymeric mesh network.12771 Microrobots in ocular surgery has shown potential applicability in drug delivery for retinal vein occlusion and surgery for manipulation and peeling of the epiretinal membrane. Another type of small-scale surgical robotics consists of ultrasound powered microrobots. These devices offer high power "bullet-like" acoustic droplet vaporization ignition mechanism. The microbullet consisted of a hollow tube (5 m diameter) filled with perfluorocarbon emulsions electrostatically interacting with the interior surface of the hollow structure. The application of high intensity focused ultrasound pulse directed at the microbullet, vaporized the perfluorocarbon emulsion, rapidly changing its state from liquid to gas, serving as a propellant. Such remarkable speed provided enough thrusts for deep tissue penetration, ablation, and destruction (Figure 9d).[91] A modified version of this design results in a functional microscale cannon, where the hollow conical structure was filled with a hydrogel containing $1 \ \mu \mathrm { m }$ nanobullets or fluorescent microspheres, and perfluorocarbon emulsion. The application of the focused ultrasound field resulted in the spontaneous vaporization of the perfluorocarbon emulsion, resulting in the rapid ejection of the nanobullets at high speed in the minute of meters per second. The acoustic microcannons were able to deliver nanoparticles into phantom tissue, reaching penetration lengths ${ \approx } 2 0 ~ \mathrm { \mu m }$ [278] Arrays of microcannons have also been translated into transdermal patches, consisting of hundreds of micropores loaded with the therapeutic payload and the perfluorocarbon emulsion. The re. lease kinetics were tested using phantom tissues and pigskin. The use of acoustic droplet vaporization microballistic delivery resulted in enhanced delivery of the anesthetic agent lidocaine when compared to passive diffusion or the use of ultrasound pulses by themself.1279] Using a similar firing mechanism solidgas polymeric nanocups were used for deep tissue penetration.

The nanostructure was capable of trapping and stabilizing gas nanobubbles inside its porous structure, which upon exposure to a high focused ultrasound pulse, resulted in the cavitation of the nanobubbles, inducing directional force. The nanocup operational capabilities were tested in a CT-26 tumor model, presenting enhanced extravasation of IgG model drug and penetration into the vessel wall.[280] This phenomenon also produced a detectable cavitation signal that lasts four times longer than other ultrasound contrast agents.[281] Moreover, this nancup model was used for deep tissue penetration and delivery of oncolytic viruses.[282] When comparing the use of magnetically and ultrasound powered micro/nano robotic surgeons, we should consider their intrinsic advantages. Ultrasound ballistic microrobots can apply higher mechanical force into the tissue, allowing deeper penetration and delivering therapeutic payload independent of the robot microstructure. Nevertheless, they have limited uses and would require an external magnetic field to guide them to their target location. On the other hand, most magnetic actuated robotic microsurgeons have a long-lasting operation and integrated guidance through the external magnetic field. However, they lack the mechanical force offered by the acoustic droplet vaporization methods.

# 4.3. Intracellular Delivery

More recent developments have aimed at miniaturizing robotic platforms for surgery at the individual cell level.283-286] The external spatiotemporal control and active manipulation of micro/nanorobots inside living cells have permitted to unprecedented access to the biophysical fundamentals going from gene expression or dynamic mechanical mapping of the intracellular environment to drug delivery and sensing.2871 Ultrasound powered microrobots have demonstrated the ability to internalize and propel inside individual living cells. This capability has been used to deliver genetic material inside cells. The use of magnetically powered micromotors has been applied to introduce a higher degree of control inside the cell, with the potential of subcellular surgery. There is potential of microrobotic platforms to be used for novel surgical applications, as they could come as a valuable tool for sample collections, unclogging blocked arteries, and gene delivery.

The internalization of nanomotors in cells and the manipulation of these artificial machines in the intracellular space was first reported using sound propelled nanomotors inside living HeLa cells.[288] Later, the nanorobot internalization inside cancer cells was used for sensing applications or nucleotide, protein, and cargo delivery. As sensing applications inside living cells, they detected miRNA-21, usually found in cancer cell lines, such as MCF-7. The miRNA-21 sensor consisted of a gold nanorobot coated with a fluorescent single-stranded DNA/graphene oxide and powered by ultrasound fields. The fluorophore in the ssDNA was initially quenched by the graphene oxide, while transported on the nanomotor. However, upon hybridization with the target miRNA, the dyed-ssDNA probe was displaced from the surface of the nanomachine allowing the fluorescence recovery and miRNA detection (Figure 10a).[289] As for cargo delivery, later works applied these gold nanorobot structures for efficient intracellular delivery and application, modified with a rolling circle amplification DNA strand to anchor the siRNA for gene silencing,490] with caspase-3 for cell apoptosis in acidic environments,[291] active intracellular oxygen delivery,[292] and with Cas9/ single guide RNA to knockout a GFP reporting gene.[293] Using a different and larger structure, nanoshells were internalized and acoustically propelled inside live MCF-7 cancer cells. The motion was based on acoustic streaming stress over the asymmetric surface of the shell nanomotors. These structures provided higher cargo towing capacity compared to previously described nanowires used for cellular internalization.1294] Another design consisted of sharp hollow gold shells that were ultrasound propelled. The microrobot internalization was enhanced by the assistance of a nearinfrared field, inducing penetration by a photomechanical perforation of the cell membrane.295] Stimuli-responsive intracellular nanorobots were also used to deliver cargo inside Hela cells. These nanorobots consisted of mesoporous silica nanoparticles, including urease-based chemical engines driven by urea present in the media and pH-responsive supramolecular nanovalves for cargo-release. The benzimidazole/cyclodextrin-urease caps were only opened at low pH delivering the doxorubicin cargo intracellularly at the lysosomal compartments of the Hela cells.[296] Mesoporous Janus microrobots were used to thermomechanically percolate inside a cell membrane under NIR irradiation toward releasing doxorubicin (Figure 10b).[297] In contrast to ultrasonically or chemically powered techniques that perturb the entire experimental volume, microsurgery using magnetic motors only perturbs the robot and the cell microenvironments.

A plant-based microrobot capable of dual single-cell microsurgery along with drug-was reported. These multitask porous microneedles were composed of biocompatible calcium salts possessing significant fabrication advantages compared to other synthetic cleanroom-based microdaggers. Coated with an irontitanium layer, they were driven to cancerous/infected cells, such as HeLa cells under the influence of an external rotating mag. netic field. Loaded with the anticancer drug camptothecin, active at acidic $\mathrm { p H } ~ 5 – 6$ they were capable of selectively release the drug in the partially acidic microenvironment of the tumor. These two functionalities significantly limit the inadvertent side effects on healthy tissue associated with treatment regimens like that of chemotherapy, among others. Further steps improving the adhesion of magnetic coating on the surface on some of the biotubes will still be required. However, the biogenic (plantderived) structure assures a biocompatible and robust controlling microsurgery tool with natural cargo-loading and delivery capabilities.[298]

A needle-type microrobot was used for paclitaxel targeted drug intracellular delivery.[299] Magnetic nanospears based on gold-nickel-silicon were designed and applied for precision, and targeted intracellular delivery of nucleic acids (Figure 10c). Nanomotors were functionalized via layer-by-layer assembly on the external spear layer with plasmids expressing enhanced green fuorescent protein. The magnetic guidance of the nanospears into the U87 glioblastoma cells allowed the penetration into the cell membrane and the intracellular delivery of the plasmid. High rates of plasmid transfection into the cell were achieved and monitored via fluorescence detection, after complete protein expression inside the cell.300] In a similar direction, spiky pollen grain microrobots (urchin-like) were used for cell drilling. The sharp structure injected doxorubicin into HeLa cells by rolling directly over the cell membrane. The sharp edges detached from the cell as the main structure continued to rotate parallel to the substrate, while the sharp edges drill into the membrane (Fig. ure 10d).[301] In another example, titanium-coated nickel mag. netically powered micro/nanorobots were used for transfecting human embryonic kidney cells. The microrobot was loaded with lipoplexes containing plasmid DNA. The outer cationic lipid lipofectamine enabled the efficient transfection of nucleic material into mammalian cells by fusing with the negative membrane and further endocytosis.1302] Nevertheless, some of these magnetic material have weak magnetic remanence (iron oxide) or are not biocompatible (nickel). Microhelix composed of iron platinum reported a higher biocompatibility and maintained strong ferromagnetic proprieties. Intracellular transfection of plasmid was used by active targeting of carcinoma by inducing cell expression of green fluorescent protein.[303] Magnetic nanorobots have also been used for intracellular surface-enhanced Raman spectroscopy.[304] The development of biocompatible magnetic materials is of great importance to reduce risk and improved performance.

![](img/f418d710bc664de3f01388baf07ef51cb84489279dadd0b52a91ebbc82499a70.jpg)  
igure 1o. Minnrot bas intracula intlizatio. ) Ultasund mot deliry f mi Redced with psion] opy right 2015, mt. R    la lie  th pih 20ie. c) Magntic mrosper delivering plasmids intocel. duced with pmsson.30 opyright 2019, American hmical Scit. d) Urchnike microperforator for intracellular payload delivery. Reproduced with permission.[301] Copyright 2020, Wiley.

# 4.4. Biofilm Degradation

Biofilms and bacterial infections represent a challenge in treatment as diverse pathogens proliferate and colonize different areas of the body resulting in numerous diseases.[305] Moreover, biofilms are typically resistant to antibacterial therapeutic, indicating the need of physical methods of treating disease.[306] In this direction, different micro/nanorobotic platforms have been employed for mechanically dislodging bacterial pathogens. Magnetic rotating nanowires were utilized to break apart an Aspergillus fumigatus biofilm mechanically. The use of rotating nanorobots in combination with an antimicrobial therapeutic agent increased bacterial killing efficacy.[307] In other cases, a biohybrid micro/nanorobot, composed of the integration of mag. netotactic bacteria (MSR-1) with mesoporous silica loaded with ciprofloxacin (antibiotic), was explored to apply mechanical stress to E. coli biofilm.308] Urease-powered micro/nanorobot were used for selective targeting, penetration, and treatment of bladder cancer. The micro/nanorobot contained anti-FGFR3 antibody to selectively bind the outer surface of the 3D cancer spheroids (Figure 11a). Once in there, the ammonia byproduct produced locally enabled the decomposition of the engine, urea, which resulted in a high suppression of spheroid proliferation. A similar strategy could be applied to other biofilms.309] Moreover, catalytic antimicrobial robots were applied to degrade and destroy different models of biofilms. These microrobots consisted of a hydrogel body loaded with iron oxide nanoparticles that serve as the dual function of propulsion via an external magnetic field and as the antibacterial agents. This robotic platform served to swept and remove biofilms over a flat surface, through a blocked capillary tube and to clean biofilms inside an interior tooth model (Figure 11b).[310] Hence, microrobots for mechanically destroying biofilms could be expanded to remove blood clots and clean arteries.

![](img/52ca3f51491990fe6485f5fc6c2a873a40690bc373dbc7f5ac4e0100b7ccfaa6.jpg)  
Figure 11. Bioflm deradation. a) Urea-powered icromotor fr eradation of cancer spheroids. Rroduced with permisson.[309 Copyright 2019 American Chemical Sciety. b) Magntic micomotor forcatalytic ioflm deadation. Reproduced with pmissin30] opyright 2019, AAAS.

# 5. Diagnosis

Motile micro/nanorobots offer unique opportunities for diagnosis, where the microrobot induces an increase in target receptor interaction and fluid mixing. Selective recognition agents of target molecules, including nucleic material, protein, cells, and ge netic material, can be employed for analysis and are part of the diagnosis and detection of different biological targets in complex and heterogeneous environments.

# 5.1. Biosensors

Micro/nanorobots have demonstrated capabilities in biosensing to detect diverse biological targets based on changes in motion or fluorescence quenching.[311] For example, Sanchez's group reported a Forster resonance energy transfer (FRET)-labeled triplex DNA pH sensitive nanoswitch for pH-monitoring of microenvironments (Figure 12a). The DNA sensor detected the ammonia produced as a byproduct of the urea decomposed by the engine of the microrobot.312] A sandwich DNA strand tag with silver nanoparticles was used to detect the $\mathsf { A g } ^ { + }$ ions induced by the acceleration of a chemically propelled nanorobot. The motiondriven DNA-sensing concept relied on measuring changes in the speed of the nanorobot. The concentration-dependent distance signals were optically visualized. Quantification down to $4 0 \mathrm { m o l }$ DNA and bacterial RNA targets without isolation or purification steps was achieved through the motion of the nanorobot.[313]

Microrobots functionalized with oligonucleotide probes were applied to detect the complementary nucleic chain of a target DNA or RNA (Figure 12b). Once the target nucleic acid was captured via DNA hybridization, a secondary fluorescent antibody tag provided a signal for quantitative evaluation. The microrobot was capable of isolating samples from different environments including buffer, saliva, and urine.[314] Another motionbased DNA sensing approach was based on catalyze powered microrobot containing single-stranded DNA capable of capturing a large size particle functionalized with another DNA strand. The microrobot and the cargo oligonucleotides strand are not complementary, but their terminals are complementary to a larger strand that serves as a bridge between them. Thus, when this strain is present in solution the movement of the cargo particle will be detectable through optical microscopy.[315]

By a similar principle, nanorobots were interfaced with loopmediated isothermal amplification of HIv-1 (Figure 12c). The presence of HIV-1 RNA strands in solution induced the loopmediated isothermal amplification forming a large stem-looped amplicons that reduced the speed of the nanorobots. This change in acceleration was measured with a 3d printed microscope system attached to the cellphone. This portable platform could detect HIV-1 virus in patient's samples showing good sensitivity and specificity.[316] The DNA sandwich hybridization strategy was expanded based on the functionalization of the nucleic probes inside the engine form a chemically propelled rockets using complementary DNA strands attached to platinum nanoparticles[317] and catalase-loaded DNA strands.1318-320] The speed of the microrobot was correlated with the concentration of the DNA target. Microrobots were functionalized with aptamer receptors capable of binding, transporting, and isolating thrombin. The experiments demonstrated high selectivity by successfully discriminating against nontarget proteins present in high concentrations.

![](img/b5f1bf85d4712145f60818357c4c6e2d8332a80c97f73c4c44162f8da912d9f7.jpg)  
Figure 12. Micnobot for bsesig ) Fluorc ssor for H monting ofthe surroundg micvioment sing FRT-abetrie DNA-based motor. Reproduced with perission312] Copyright 2019, America Chemical Sciet. b) Micromotor with ligonucleotide probes for detction f mpl ctrn. cd wh ss3 ght 2011, Amrican l t. ood thmal amplificatio for motion-aed esing. Rdced with psson316] Copyright 2018, Springer Nature.d) ik virus ction based n motionbased immunoassay. Reproduced with permission.1327] Copyright 2018, American Chemical Society.

The subsequent release of the protein was achieved by the addition of adenosine triphosphate molecule that binds with the aptamer displacing the captured thrombin. The amount of protein capture was measured quantitatively by the addition of a secondary fluorescence tag.[321]

Cationic branched polyethyleneimine coated microrobots were used to extract nucleic acid from samples by employing reversible surface charge by changing the environmental $\mathrm { p H }$ . The surface charge can absorb the nucleic acid in low $\mathsf { p H }$ environment and deabsorbed them in high pH environments, thus presenting a simple method for collection and release of DNA.[322] Microrobots functionalized with boronic acid were used for target binding of polysaccharide. The cargo release was achieved by the addition of fructose that had more affinity than the polysaccharide. The surface of the robot had target recognition sites that bind to specific molecules, demonstrated by specific binding of fluorescein isothiocyanate-labeled avidin.1323] Similarly, a Janus based micromotor coated with molecularly imprinted layer was reported for propranolol recognition. The functionalization was achieved via a wax-water pickering emulsion that achieved the generation of propranolol-imprinted sites by cross-linking polymerization.[324] Multiplexing targeting was also achieved by using nanorobots. This work demonstrated the specific binding of the functionalized gold nanowires with IgG, myoglobin, and thrombin.1325] Microrobots functionalized with streptavidin demonstrated to capture biotinylated fluorescent beads. Microfluidic preconcentrating chambers increased the ability of the motor to capture the bead.[326]

A Zika virus detector was demonstrated based on the motion of a nano/microrobot. This robotic sensor relied on a sandwich assay that employed anti-Zika mAb coated nanorobots and antiZika mAb coated microbeads. The interaction of these components resulted in the motion of the microbead (Figure 12d). A change in speed of the microbead was detected using a cellphone that indicated different concentrations of the virus. This motionbased strategy presented a high specificity in the presence of other viruses, including dengue, herpes simplex virus type 1 and human cytomegalovirus.327] Other micro/nanorobots were also sensors for ricin B toxin detection based on an "off-on" fluorescent mechanism. The microrobot was composed of graphene oxide external layer which can bind to the aptamer tagged with fuorescein-amidine dye. The target loading due to $\pi - \pi$ interactions, resulted in the quenching of the fluorescent probe. The preferential biding between the ricin and the aptamer results in an increase of fluorescence, thus allowing to measure ricin concentration by optical readout.328] Similarly, reduced grapheneoxide (rGO) functionalized tubular nanorobots were demonstrated for rapid, cost-effective, real-time detection, and effective isolation from mycotoxins (fumonisin ${ \tt B } _ { 1 }$ and ochratoxin A). The operational principle was based on selective recognition of aptamers to target mycotoxins isolation and quantify them based on quenched fluorescence intensity variations. Finally, these rGO functionalized nanomotors showed high sensitivity, selectivity, and suggesting potential applications in biosensing.[329] A Janus microrobot functionalized with quantum dots presented the use of "on-off' fuorescence detection, based in the use of phenylboronic acid which functioned as a recognition receptor for bacterial endotoxins.1330] Furthermore, an "on-off' fluorescent approach was applied for detecting Clostridium difficile (C. diff), which are secreted toxins in a portable and efficient mobile platform. The surface contained functionalized carbon dots that change through fluorescence emission based on the presence of the target toxin. This quantum dot based microrobot protocol was further expanded to isolate C. diff toxins discharged from patient's stool. The nanorobot locomotion was controlled by an external magnetic field, which increased the interaction of the toxin with the surface of the nanorobot. The absorption of the toxin over the microrobot surface resulted in the quenching of the quantum dot fluorescence, serving as an optical readout to quantify the presence of C. diff toxins in solution (from 0.38 to $1 7 . 8 0 \mathrm { n g } \mathrm { m L } ^ { - 1 }$ .[331] Magnetic plasmonic gyro-nanodisks (GNDs) were used to detect influenza virus (HA1). The working principle was based on the magnetic plasmonic response of the GNDs. These GNDs produced magnetic response and surface plasmon polarization under periodic excitation of an external rotational magnetic field that covert-induced plasmonic effect into frequencies. Finally, the Fourier-transform technique was employed to covert increased share stress of GNDs as frequency shift to captured biotarget, such as the influenza virus (HA1).132] Other immunoassays-based microrobot have been applied to detect carcinoembryonic antigen,[33] proteins,[334] bacterial toxins,335] cortiso1,[336 glucose,337-339] sepsis,[340] and $\beta$ galactosidase.[341]

![](img/909a2c708b717c063375b4b51e2ddd475234df3f98ccfac4b96189b9a657b160.jpg)  
Figure 13. int bas iio Mt fuctionalid wth antidie t it tt ateria. Rdcd wth p345] Copyright 2012, American Chemical Society.b) Noncontact manpulation of cancer cels using rotating microrobots. Reproduced with pemisson.32] Copyright 018, American Chl city.) Rd lod cel/ateet td not for sygistic slation f athgs and toxin. roduced with permission.1358] Copyright 2018, AAAS.

# 5.2. Isolation

Current techniques for isolation and purification of biotargets require long incubation times and multiple washing steps.342]

Functionalized micro/nanorobots have been described as "onthe-fly" platforms for the rapid isolation of biotargets and infectious pathogens.[343,344] For example, nanorobots functionalized with the bioreceptor concanavalin (conA) were used as a tool for real-time isolation of E. coli (Figure 13b). ConA is a mannose- and glucose-binding protein that interacts specifically to the polysaccharid surface of Gram-negative bacteria. The released of captured bacteria was triggered upon decrease of pH through a glycine-based dissociation solution.1153,345] A similar approach with Anti-ProtA antibody functionalized microrobots was capable of isolating S. aureus, which expresses the complementary protA antibody on its outer membrane. This isolation method presented good selectivity to the target even in excess of yeast cells in solution.346] Orozco et al. reported anti-Bacillus globilli (B. globilli) antibody functionalized microrobots to isolate single and multiple B. globigi spores in complex biological microenvironment.[347]

Using a nonspecific isolation approach, E. coli was isolated from contaminated water samples based on a "sticky" chitosan hydrogel-surface coating, which enhanced trapping and killing the pathogens.348] Microrobots functionalized with anticarcinoembryonic antigen (anti-CEA) antibody reported the ability to isolate cancer cells from complex media. The isolation mechanisms were based on specific recognition of the antiCEA antibody with the surface antigens overexpressed by pancreatic cancer cells.[349] Cancer cells have been also isolated by using physical stimulation. For example, chemical[350] and magneticl351] microrobots were able to "pick and drop" cells by pushing them. In another case, peanut shape colloid reported the noncontact fluidic manipulation and transport of cells based on the generation of localized microstreaming flows capable of trapping the cell (Figure 13b). This principle was explored to isolate and transport multiple cells inside microfluidic holes.[352] Similar nonmanipulation of living cells has also been achieved using acoustic streaming.[353,354]

Blood cell-based coating has also been described for the rapid isolation of pathogens and toxins. Ultrasound[355] and chemically1356] powered nanorobots coated with red blood cell membranes have been demonstrated as motile sponges for isolating toxin in biological environments. This approach served as a decoy to absorb toxins that would commonly bind and kill red blood cells. Moreover, magnetic helical nanorobots coated with plasma membrane of human platelet, which showed platelet mimicking properties, including platelet-adhering pathogens, such as S. aureus.[357] The integration of a hybrid cellular membrane coating on the surface of the nanorobots resulted in a synergistic isolation of both pathogen and toxin (Figure 13c). The platelet membrane isolated the pathogen S. aureus, while a red blood cell membrane absorbed the $\alpha$ -toxin absorption secreted from the captured pathogens.[358]

![](img/5d30972815d4a59040dc65db5a90ca22fc443b8271693c76b9bc5caf96a8cb30.jpg)  
Figure 14. Micro/nanorobots as mechanical probes. a) Nanorobot of measuring intracellular mechanical properties. Reproduced with permission.1359] Copyright 2018, Wiley. b) Nanorobot for biofluid rheology. Reproduced with permission.364] Copyright 2016, American Chemical Society. c) Nanorobot for measuring mechanical forces of a macrophage. Reproduced with permission.[369] Copyright 2017, AAAS.

# 5.3. Physical Sensor

Micro/nanorobots have potential as label-free biomechanical probes. For example, helical nanorobots driven by rotating magnetic fields were used to probe the mechanical properties inside living cells (Figure 14a). This nanomotor faced the challenge of low adherence to their surrounding environment inside the cells, which in certain cases, can lead to reducing mechanical response. However, motion of helical magnetic nanorobots was successfully explored in Hela, kidney, and endothelial cells environments after incubation and internalization.3591 This robotic intracellular probe approach was also demonstrated to measure viscosity,[360] and adhesive forces inside metastatic cancer cells.1361] Piezoelectric microrobots were also reported as wireless probes for neural stimulation mapping and differentiation of single cell using the piezoelectric effect produced by an external ultrasound field.1362] For example, electrically powered microrobot were used as motile electrodes capable of deforming cells, in which the resulting distortion of the cell nucleus correlated with detectable dielectrophoretic potential wells.[363]

Viscosity is an important parameter for medical applications. Nevertheless, the measurement of rheological properties in biological fluids is challenging as they contain a mixture of multiple biocomponents. Rotating nanorobots coils as mechanical sensors were also studied for determining the rheological properties of a microscopic volume (Figure 14b). The rotating nanorobot probed the viscosity of the solution by measuring the lag phase between nanorobot orientation and the external oscillating mag. netic field. Thereby, determining the torque applied to the particle by the medium. Because the nanorobots were smaller than the suspended red blood cells, they could selectively probe only the fluid phase.[364] Chemically powered microrobots were also explored as viscometers. The velocity of the microrobot allowed to estimate the fluid viscosity of a solution.[365] Similarly, different physical parameters (pressure and flow rate) were obtained based on the propulsion of a microrobot.[366]

The study of microphages hunting behavior and immune host defense at the micro-environment presents a challenge due to the lack of physical probes that can interact with phagocytes in a similar fashion that pathogens.1367,368] In this direction, magnetic microrobots with 5-degrees of freedom were studied to mimic the predator-prey interactions, hunting, and phagocytes behavior in macroscopic biological organisms, particularly in the immune host defense at microscale environment (Figure 14c). Dynamic translational resistance or rotational forces were also applied as a magnetic torque to arbitrarily position microrobots as prey near to the microphages, and measure the exert forces, and rotation torques as optical displacements.[369]

# 6. Medical Imaging

The transition from in vitro to in vivo research has addressed the need to integrate microrobots with medical imag. ing platforms.1370-372] A key aspect for medical micro/nanorobotic translation in the clinic will rely on individual or population monitoring, with consideration of tissue background signal.[373] In this direction, micro/nanorobots could benefit the current medical imaging modalities, as they could be easily localized and guided inside the body, and even send signals to induce triggered release. Current conventional medical imaging techniques study organ and tissue physiology, and more recently, track the distribution of molecular imaging agents and nanoparticles inside a patient's body. The ability to monitor and guide individuals and groups of nanomotors inside the body will be essential to achieve their widespread application. Therefore, the unique advantages of each imaging platform and processing methods should be considered, as each imaging modality is better suited for different organ compositions and depth-of-focus inside the body.[374] The main challenge, regarding untethered micro/nanorobotic research, is the ability to distinguish the micro/nanorobot structure from their environment (background subtraction) with sufficient acquisition resolution to track each step of motion in a 3D environment. Due to the complexity of the data acquired, algorithms, instead of medical staff would be required to identify and follow the microrobot motion through the body. In this direction, we need to consider how to tag each micromotor by incorporating contrast imaging materials or distinct engineered shapes that allow us to limit the effect of background noise. The imaging modalities used in combination with micro/nanorobots include optical, magnetic, acoustic, and radionuclide imaging.

# 6.1. Optical Imaging

The diagnosis of the disease remains a challenge for clini cians and researchers, as most cases are asymptomatic un til advanced stage due to lack of sensitivity and specificity to accurately detect premalignant lesions. In this case, micro/nanorobots can enhance current imaging capabilities. Initial works in micro/nanorobots employed optical methods, which included catheter cameras and light radiation, which offer a robust imaging capability at a relatively low cost. Thus, optical cameras have shown the position of micromotors inside a rabbit eye,[275,375] and with an endoscopic camera a microgripper inside a pig bile has been visualized.1258] Although handy, these ingestible cameras have limited access inside the body, can cause patient discomfort and, in some cases, are not able to detect nano/micrometer size structures. In this direction, noninvasive fuorescence imaging could help achieve a high-resolution quantification and localization of microrobots. In vivo fluorescence of organic molecules or inorganic fluorescent nanoparticles was monitored with a charge-coupled device camera which captured the light emitted from an animal bodyl2291] (Figure 15a). The fluorescent signal was further overlaid over the actual picture of the animal allowing a spatial localization of the molecular imaging agent. Thus, the widespread application of imaging microrobots inside the body is easily achievable by modifying the surface of microrobots with fluorescent molecular imaging agents.

Fluorescence imaging techniques have thus typically studied to visualize the position of micromotors in living animals. Swarms of magnetically actuated helices functionalized with near-infrared probes (NIR-797) where monitored inside the peritoneal cavity of a mouse using whole-body fluorescence imaging.1376] Spirulina microalgae coated with superparamagnetic magnetite $\left( \mathrm { F e } _ { 3 } \mathrm { O } _ { 4 } \right)$ nanoparticles was applied to track a swarm of micromotors located inside the subcutaneous tissue and the intraperitoneal cavity of nude mice using fluorescencebased in vivo imaging.13771 Other of the multiple examples of biohybrid microrobot as carriers of imaging agents is one of the first examples of micromotor imaging which consisted of a bacteria biohybrid microrobot loaded with $4 0 \mathrm { n m }$ nanoparticles that contained the luciferase gene. These micromotors were injected intraperitoneally into a mouse for the delivery of a luciferase DNA plasmid inside nearby cells and expressed the luciferase protein, which resulted in the luminescence in different organs.l378] Similarly, the migration of biohybrid microrobot bacteria loaded with $\mathrm { C y } 5 . 5$ -coated polystyrene microbeads was imaged inside a tumor site by the signal produced by fluorescent dye.[379] Moreover, microrobots carrying living cells have been imaged using fluorescence emitted from the cell229,230] In these cases, the fluorescent imaging capabilities where essential to illustrate the successful and target delivery by microrobots into its desired location.

There is still room for improvement in optical imaging for micro/nanorobots. Thus, recent works have aimed to distinguish individual micro/nanorobots, taking advantage of Janus microrobot asymmetric "reflective" properties. In this case, the use of a customized optical IR imaging setup coupled with an image correlation software was employed to monitor single unlabeled microrobots in real-time underneath phantom and ex vivo mouse skull tissues. The change in reflectivity of micromotors enable their tracking through phantom thicknesses.[380]

# 6.2. Ultrasound Imaging

Ultrasound imaging is another candidate that offers a biocompatible cost-effective alternative to visualize micro/nanorobots in real-time. The interaction of the ultrasound pulse with tissue containing different reflection properties produced a distinct echo that is recorded and transformed into an image. Although ultrasound external field is widely developed to power micro/nanorobots, the employment of ultrasound as an imaging modality remains limited.1381] In vitro studies have illustrated the ability to detect the position of a micro/nanorobot that propels through solutions by catalyzing hydrogen peroxide into a trail of oxygen microbubbles (Figure 15c).382] The main limitation of this system is that the imaging system is detecting the microbubbles responsible for propulsion, but not the micromotors themselves. Therefore, the obtained data points would have to be processed to provide an accurate position of the micro/nanorobot. The main challenge of ultrasound relies on its limited resolution and limited selection of contrast agents, consisting mostly of stabilized microbubbles. To solve this limitation, the optoacoustic imaging system offers the resolution and penetration depth of ultrasound combined with the specificity of optical methods. Photoacoustic imaging working principle consists on the emission of optical pulses that are absorbed by tissues, resulting in a thermoelastic expansion that generates ultrasound waves. A transducer is then able to collect the information and transform it into images of the molecular imaging agents inside tissues. A recent study illustrated the ability to visualize micromotors coated with molecular imaging agents, inside an ex vivo chicken breast model, with high contrast and specificity.380] In another case, the ability to localize micro/nanorobots with chemically propelled engines inside a living mouse was achieved using photoacoustic imaging (Figure 15b). Additional near-infrared light irradiation is also able to activate the propulsion of the microrobot by disintegrating a protective layer.1383] More recently, magnetotactic biohybrids coated with a polydopamine layer, which enhanced the photoacoustic detection signal and subsequent photothermal therapy were also reported. This study served a proof of concept for the treatments of pathogens in an in vivo model.[245]

# 6.3. Magnetic Imaging

Magnetic imaging is one of the most robust methods to image micro/nanorobotic structures inside the body. Magnetic imaging resonance (MRI) employs magnetic fields to visualize biological tissue with a high degree of spatial resolution and contrast. The imaging mechanism is based on the absorption and re-emission of electromagnetic radiation or hydrogen nuclei under the presence of a strong magnetic field. The energy produced during this event is collected and process to give insight into different physical and chemical information about the molecules at the imaging locations. Different contrast can be generated by fine-tuning the magnetic field pulses. In particular, when compared to optical imaging, magnetic imaging has higher resolution and penetration depth. Moreover, it also reduces the undesired side effect of ionizing radiation of X-ray imaging. In the context of micro/nanorobotic research, magnetic materials embedded in the micro/nanorobot structure can generate distortion in the incident magnetic field, producing a signal in the resulting image. The magnetic susceptibility of the materials allows them to be distinguished from nearby tissue. In recent years,

![](img/f7bc3815eced6852e15144c6be5fdbe5092af5db2459877eac1a191d9d918496.jpg)  
Figure 15. Medical imaging f moots. ) I vio floecence imaging f magneticl riven micorobt caryin cell into a mouse flank. R prdced wth pmis Copyright 2018, s.)  o phcostic mng f a anm propelld mr t t a mose intestine. Repd wth p t 013r.  mng b  y cll robot. Reproduced with permission.1382] Copyright 2019, AAAS.

MRI imaging has been applied to visualize magnetic structures inside small mammals. Recent works have used magnetic helical microswimmers made from Spirulina microalgae coated with superparamagnetic magnetite $( \mathrm { F e } _ { 3 } \mathrm { O } _ { 4 } )$ nanoparticles.[377] In this case, the magnetic coating serves as the engine to convert the external magnetic field into motion and as an imaging contrast, eliminating the need for any further surface modification. In vivo experiments with these robots demonstrated the ability to track a swarm of these microstructures inside a mouse stomach (Figure 16a).

MRI has also shown applicability for wirelessly guiding microswimmer's through the body[384] and as a therapeutic tool via magnetic hypothermia.1385] Nevertheless, these works include different fields for imaging and guiding. Therefore, the operation of both types of fields at the same time is a challenge, reducing the ability to perform operations in real-time. Alternatively, when both fields are applied at the same time, there could be a delay be tween microrobot actuation and image processing.

![](img/6ae3ccb78dd030bf27901476e0f9bd48e98cb37b6af25c5ffeb7e5ca17f2efea.jpg)  
Figure 16. Other medical imaging applications. a) Magnetic resonance imaging of helical micromotors for intragastric region Reproducec with pemission37]Copyright 2017, As. b) Radionuclotide based imagin of microrocket coated with ioine isotope. Reproduced wit permission.[386] Copyright 2018, American Chemical Society.

# 6.4. Radionuclide Imaging

Radionuclide imaging technologies are also another powerful tool in medical imaging. They have unique advantages, as they offer molecular information and sensitivity. Proton emission tomography (PET), is based on emitting positrons that can break down radionuclides, thus, generating gamma rays that a scanner can detect and use for mapping the studied region. Different nuclides can be employed to target specific organs or biological processes. Although the radiation offers deep tissue penetration, it could result in adverse health events in prolong uses, thus limiting the available time to image. A PET system was used to track a large group of Au/poly(3,4- ethylenedioxythiophene/Pt chemically propelled microrobots coated with iodine isotope (Figure 16b).1386) Soft thermoresponsive magnetic microrobots were used for emission computed tomography imaging.13871 X-rays have also shown limited examples for microrobotics.38s] X-ray radiation has been demonstrated to power Janus microparticles,[389] and to detect star-shaped microgrippers inside the gastrointestinal tract.[258] As a general overview of the medical imaging section, each method has its unique advantage. Optical and ultrasound imaging have a relatively low-cost, although inferior penetration depth when compared to magnetic and radionucleotide imaging.

# 7. Outlook

In summary, the use of microrobots in precision medicine has shown a diverse plethora of applications in different fields including the delivery of pharmaceuticals, biologics, genes, and living cells; surgical tools for biopsy, tissue penetration, intracellular delivery, or biofilm degradation; diagnostic tools including physical and chemical biosensors or isolation tools; and optical, ultrasound, magnetic, and radionuclide imaging tools (Table 1). The most developed application is target delivery and current efforts are mainly focused on animal testing. However, imaging is required in combination with delivery, surgery, or diagnosis to understand the dynamics of the micro/nanorobots and leverage their efficiency and capabilities. Current research of nano/microtools suggests the constant narrowing of the gap between precision medicine and micro/nanorobotics. Nevertheless, each application faces different challenges toward potential clinical translation. For delivery and surgery applications, micro/nanorobots would require operating in hard to access regions of the body, therefore recovery/degradation strategies are of great significance to ensure they would not pose a risk to the patient health. Diagnostic tests are often conducted in vitro, therefore the main challenge for microrobots as diagnostic tools would rely on improving their scalability and enabling high-throughput detection. The main challenge in medical imaging applications is the ability of each imaging modality to distinguish in real time individual and large groups of micro/nanorobots forming a background tissue. This challenge would require high frame rate acquisition at high magnification, thus producing large quantities of data. The use of machine learning algorithms could enable to rapidly evaluate the data forclose loop operation of microrobots in vivo.

Table 1. Overview of current trends of micro/nanorobotics in Precision Medicine, incuding dlivery urgery, diagnosis, and medical imaging   

<html><body><table><tr><td>Application</td><td>Description/justification</td><td>Examples</td><td>Challenges</td></tr><tr><td>Delivery</td><td>Use of micro/nanorobots to deliver target payload with higher spatiotemporal resolution versus passive diffusion</td><td>Pharmaceuticals Biologics Living cells Inorganic therapeutics</td><td>Dosing Selective release Biodegradation-retrieval</td></tr><tr><td>Surgery</td><td>Use of micro/nanorobots as surgical tools to access remote locations, while aiming to reduce invasive surgical</td><td>Biopsy/sampling Tissue penetration Intracellular delivery Bioflm degradation</td><td>Force generation Biodegradation-retrievalSample recovery</td></tr><tr><td>Diagnosis</td><td>Use of micro/nanorobots to enhance fluid mixing and mechanical force to increase sensing capabilities</td><td>Biosensor Isolation Physical sensor</td><td>High throughput ScalabilityCost</td></tr><tr><td>Imaging</td><td>Provide feedback loop mechanism to enable other applications and exploration of the body</td><td>Optical Ultrasound MagneticRadionuclide</td><td>Higher resolution Higher frame rateData processing</td></tr></table></body></html>

Moreover, micro/nanorobots face safety, technical, regulatory, financial, and market challenges to be overcome before they are translated to clinical uses. We will itemize those in detail below. Although there is a long road ahead, the use of microrobots in precision medicine has the potential to improve diagnosis and treatment, which could lead to improving a patient's life. Microrobots could help in precision medicine and reduce the cost and discomfort associated with major surgical procedures.

# 7.1. Safety and Biocompatibility

Ensuring the safety and biocompatibility of medical micro/nanorobots is a critical prerequisite for their widespread implementation in the clinic.390] 1deally, the micro/nanorobot will perform its intended function by either delivering a cargo, performing surgery, or providing a diagnostic signal. Then, after completing its task, it would be reabsorbed in the body or retrieved by a medical device or by excretion. The degradation of the micro/nanorobot can be tuned for a specific biological environment or by time-dependent materials proprieties of the robotic device.1391-393] On the other hand, catheters like devices would retrieve the micro/nanorobot.1394,395] The material composition of the micro/nanomotor will determine how long their structure could last inside the body. For example, most magnesiumbased micromotor engines are fully depleted, while PLGA-based magnetic microengines could last for days. Moreover, the introduction of foreign materials could produce an inflammatory response by the patient's immune system.[396] Therefore, designing micro/nanorobots with biocompatible coatings with tunable surface charge, stretchability, hydrophilicity, and morphology; could limit adverse results and expand their longevity. Micro/nanorobots are not currently used in clinical trials, but initial progress has been made in preclinical animal tests reporting hematology and blood chemistry with minimal adverse effects.17,371 Ethical considerations should be kept in mind when designing micro/nanorobot animal experiments, as these initial tests increase the minimal risk for the animal without direct benefit. Moreover, the results obtained by animal studies do not always translate to humans. Nevertheless, they have the potential to yield generalizable knowledge with translational benefit to a general patient population. No micro/nanorobotic technology covered in this review has been tested in humans. Therefore, there is still much work to be done to evaluate the long terms side effects that micro/nanorobots could have on human health.

# 7.2. Technical and Regulatory Challenges

In the realm of technical challenges, mass fabrication of the microrobotic system presents a problem to solve. Over the next decade, with the development of sophisticated bio fabrication manufacturing techniques and continuous innovations on biocompatible materials, micro/nanorobots could become safe to use in clinical applications.397,398] In this direction, researchers should focus on the use of novel high throughput fabrication methods that assists in the interfacing of tissue engineering and nanoengineering. Such development fulfills the needs of functionality and biocompatibility required by medical devices.[399] These individual parts could be assembled into more sophisticated modular structures. For example, our group has reported the use of magnetic assembly methodologies to build untethered magnetic micro/nanorobots powered by magnetic fields. This type of hydrogels-based robot was composed of multiple building blocks to create a cooperative assemble of parts capable of interacting with their environment and performing predetermined tasks. The microrobot was fabricated by incorporating magnetic nanoparticles inside the hydrogel (Figure 17a).[225] An external magnetic field was used to guide the self-assembly of the magnetic hydrogels into patterned 2D and 3D modular structures with different designs and material proprieties, such as mass density, porosity, and elastic modulus.400,401] Another type of magnetic assembly based on magnetic levitation was used to self-assemble living cells into complex configurations embedded into a 3D extracellular matrix (Figure 17b).1402] The use of magnetic levitation is based on the use of two magnets with the same poles with a microfluidic channel embedded between them. The cells are resuspended in a cellular medium containing dissolved gadolinium-based paramagnetic agents that, in conjunction with the magnetic field is capable of inducing force to levitate living cells based on the difference between the magnetic susceptibility of the medium and cells.1403] Magnetic levitation has unique advantages including the ability to use different types of cells with no requirements for magnetic nanoparticles that would need to be removed later from the constructs.404] Recently, magnetic levitation has been used to guide the bioassembly of 3D tissue constructs in space marking a seminal advance in 3D bioprinting and biofabrication.1405] Another promising biocompatible technique consists on the use of bioacoustic-enabled fabrication for assembling a large number of microscale units (synthetic beads, cells, spheroids) into reconfigurable and ordered symmetric structures. The large-scale patterns can be modulated by fine-tuning the applied acoustic field. The bioacoustic fabrication method has unique advantages, as it eliminates the requirement of supportive materials and can preconcentrate a large number of cells in a few seconds.[406] Moreover, we have demonstrated the use of bioacoustic fabrication with types of cellular designs, including for cardiac tissue,[407] brain-like constructs,[408] ring-shaped cellular robots,[226] and organoids (Figure 17c).[409] The development of biorobots has the potential to create fully autonomous micro/nanorobots in the interface of growth and assembly. Moreover, integrating biomaterials into the robot design could increase its safety and cloak it from a patient immune system.

![](img/c4adbc046a91720494248bd79269a2dc6ff5573f62f7748e8039fa7c262c68fb.jpg)  
Figure 17. Biofabrication of microroots and cellular micostructures. a) Hydrogel robot fabricated by magnetc assembly. Reproduced with pemissio25 pyright 2014 Springer Nature. b) Tunabl fabriction f cllar micostructures using magntic vittion. Reced with permission.40] Copyright 2018, Wiley.c) Bioacoustic farication f organoids. Reproduced with permission.409] Copyright 2015, Wiley.

Micro/nanorobots have been tested in diverse regions of the body including the gastrointestinal tract, blood vessels, brain, and knee cartilage (Figure 18a). Nevertheless, a challenge moving forward relies on demonstrating their safety. There are multiple reg. ulatory bodies, such as FDA agency in the United States, China Food and Drug Administration in China, European Medicines Agency (EMA) in Europe, Japan Pharmaceutical Manufacturers Association (JPMA) in Japan, among others that will evaluate and approve the use of micro/nanorobotic platforms in the clinic. To pass such regulatory hurdles, new technologies need to demonstrate their safety and efficacy.[41,411] The probability of getting approval is historically very low and is also very costly and timeconsuming. Most regulatory agencies worldwide classify medical devices based on their potential risk to a patient's well-being. As an example, Class I is considered a low or minimum risk. Deciding which kind of purpose the micro/nanorobot will be used for, will determine their classification. Thus, affecting the time required for clinical translation. Some of the sensing applications that are performed in an external assay could be classified as class I, if their output provides a qualitative response rather than a quantitative result. A micro/nanorobot would be class II or IIl if they partially or fully penetrate a patient either through a body opening or surface. Although it might seem easier to bypass the regulatory system by choosing a lower risk application with faster ease of deployment, such as micro/nanorobotic based diagnostic platforms, a higher risk/high potential application, such as delivery or surgery could provide a competitive advantage to the micro/nanorobot in the long run, as illustrated in the prioritization table on the basis of potential and ease of deployment shown in Figure 18b. Researchers should consider outsourcing some of the clinical validation work required for FDA approval to a private research contract organization.412] Artificial intelligence and machine learning could also help to increase the safety of micro/nanorobots. Local path planning algorithms could help train micro/nanorobots to navigate in the unknown and dynamically changing biological environments, thus avoiding hitting obstacles and getting stuck inside the body.[413]

![](img/3a64312e610ccfe7ba1225f268842315c15ca6825ab24d6519a2d7c0a25f5c76.jpg)  
Figure 18. Mico/nanorobots cincal translation outook a) Opertion of micro/nanorobots in diverse regions of the body. b) Characterization medical micro/nanorobots based on their potential and ease of deployment. c) Developmental trends of emerging technologies.

cro/nanomotors have initial proof of concept in human subjects, leveraging micro/nanorobots in precision medicine will improve diagnosis and treatments, which could lead to improve patient's life.

# 7.3. Financial and Market Challenges

When analyzing the technology readiness of micro/nanorobots as an emerging technology, their clinical translation potential is still in the early stages.414 417] The typical technology maturity development of emerging technologies as develop by Gartner is shown in Figure 18c. The maturity of the field will progress based on its commercialization outlook. Therefore, one of the major barriers to their clinical translation involves securing the cost of funding early developments in university and private research. Nondilute funding is available but is quite competitive. Therefore, novel technologies could benefit from academic-industry partnerships. The main financial cost that should be considered involves scientific staff, cost of equipment and materials, regulatory costs, and intellectual property. Universities offer a unique setting for developing technological innovation as multiple fields of study (engineering, medicine, business, law) are under the same institution. Researchers in the field should expand their patenting, as technological developments without proper intellectual property protection are unlikely to secure funding from the private sector. Commercial enterprises are also essential to advance the field of robotics, as profits could be reinvested into research and development of new micro/nanorobotic technology.

Finally, there is the market risk. Although micro/nanorobots have shown promising results in in vitro and in vivo studies, they currently lack proof of concept application that offers a distinct potential advantage over the state of the art, for example, increased therapeutic efficiency, reduced side effects, discomfort, or lower cost. In order to achieve lab to market transition, commercial enterprises in micro/nanorobotics should identify unmet needs that could validate the market of medical microrobots. Initial micro/nanorobotic commercial ventures should focus on complementing existing medical devices. For example, by inte grating them with catheters to reach reduced and inaccessible regions of the body. Although there is a long road ahead, once mi

# Acknowledgements

This work was supported by the National Institutes of Health (Nos. R01 EB029805-01, R01 DE024971, R01 Al120683, and R01 Al122862-01, T32 CA118681), the ACED alliance pilot award and the Canary Center at Stanford for Cancer Early Detection. F.S. was supported by Stanford Molecular Imaging Scholars program. TOC schematics were created using biorender.

# Conflict of Interest

Prof. Utkan Demirci (U.D.) is a founder of and has an equity interest in: i) DxNow Inc. ii) Koek Biotech, iii) Levitas Inc. iv) Hillel Inc. U.D.'s interests were viewed and managed in accordance with the conflict of interest policies.

# Keywords

diagnosis, medical imaging, micro/nanorobots, microsurgery, precision medicine, targeted delivery

Received: June 11, 2020   
Revised: August 9, 2020   
Published online: October 4, 2020

[8] S. Sancnez, L. Soier, J. Katur, Angew. Cnem., Int. ta. zu15, 54, 1414.   
[9] X.-Z. Chen, B. Jang, D. Ahmed, C. Hu, C. De Marco, M. Hoop, F. Mushtaq, B. J. Nelson, S. Pane, Adv. Mater. 2018, 30, 1705061.   
[10] C. Chen, F. Soto, E. Karshalev, J. Li, J. Wang, Adv. Funct. Mater. 2019, 29, 1806290.   
[11] M. Guix, C. C. Mayorga-Martinez, A. Merkoci, Chem. Rev. 2014, 114, 6285.   
[12] S. Sengupta, D. Patra, I. Ortiz-Rivera, A. Agrawal, S. Shklyaev, K. K. Dey, U. Cordova-Figueroa, T. E. Mallouk, A. Sen, Nat. Chem. 2014, 6, 415.   
[13] J. Li, B. E. F. De Avila, W. Gao, L. Zhang, J. Wang, Sci. Rob. 2017, 2, eaam6431.   
[14] F. Peng, Y. Tu, D. A. Wilson, Chem. Soc. Rev. 2017, 46, 5289.   
[15] !: Wang, ACS Nano 2009, 3, 4.   
[16] Y. Mei, A. A. Solovev, S. Sanchez, O. G. Schmidt, Chem. Soc. Rev. 2011, 40, 2109.   
[17] T. E. Mallouk, A. Sen, Sci. Am. 2009, 300, 72.   
[18] G. A. Ozin, I. Manners, S. Fournier-Bidoz, A. Arsenault, Adv. Mater. 2005, 17, 3011.   
[19] X. Chen, C. Zhou, W. Wang, Chem. - Asian J. 2019, 14, 2388.   
[20] J. M. Jordan, Robots, MIT Press, Cambridge, MA 2016.   
[21] B. M. Vinagre, I. Tejado, J. E. Traver, Fract. Calc. Appl. Anal. 2016, 19, 1282.   
[22] E. M. Purcell, Am. J. Phys. 1977, 45, 3.   
[23] E. Karshalev, B. Esteban-Fernandez De Avila, J. Wang,J. Am. Chem. Soc. 2018, 140, 3810.   
[24] A. Ghost, P. Fischer, Nano Lett. 2009, 9, 2243.   
[25] S. Ahmed, W. Wang, L. Bai, D. T. Gentekos, M. Hoyos, T. E. Mallouk, ACS Nano 2016, 10, 4763.   
[26] H. Ning, Y. Zhang, H. Zhu, A. Ingham, G. Huang, Y. Mei, A. Solovev, Micromachines 2018, 9, 75.   
[27] W. F. Paxton, K. C. Kistler, C. C. Olmeda, A. Sen, S. K. St. Angelo, Y. Cao, T. E. Mallouk, P. E. Lammert, V. H. Crespi, J. Am. Chem. Soc. 2004, 126, 13424.   
[28] W. Gao, S. Sattayasamitsathit, J. Orozco, J. Wang,J. Am. Chem. Soc. 2011, 133, 11862.   
[29] T. R. Kline, W. F. Paxton, T. E. Mallouk, A. Sen, Angew. Chem., Int. Ed. 2005, 44, 744.   
[30] M. J. Banholzer, L. Qin, J. E. Millstone, K. D. Osberg, C. A. Mirkin, Nat. Protoc. 2009, 4, 838.   
[31] S. Tasoglu, U. A. Gurkan, S. Q. Wang, U. Demirci, Chem. Soc. Rev. 2013, 42, 5788.   
[32] Y. Wu, Z. Wu, X. Lin, Q. He, J. Li, ACS Nano 2012, 6, 10910.   
[33] D. A. Wilson, R. J. M. Nolte, J. C. M. Van Hest, Nat. Chem. 2012, 4, 268.   
[34] K. Han, C. W. Shields, N. M. Diwakar, B. Bharti, G. P. Lopez, O. D. Velev, Sci. Adv. 2017, 3, e1701108.   
[35] R. Dreyfus, J. Baudry, M. L. Roper, M. Fermigier, H. A. Stone, J. Bibette, Nature 2005, 437, 862.   
[36] G. Loget, A. Kuhn, J. Mater. Chem. 2012, 22, 15457.   
[37] A. M. Pourrahimi, M. Pumera, Nanoscale 2018, 10, 16398.   
[38] B. Jurado-Sanchez, M. Pacheco, R. Maria-Hormigos, A. Escarpa, Appl. Mater. Today 2017, 9, 407.   
[39] J. Li, V. V. Singh, S. Sattayasamitsathit, J. Orozco, K. Kaufmann, R. Dong, W. Gao, B. Jurado-Sanchez, Y. Fedorak, J. Wang, ACS Nano 2014, 8, 11118.   
[40] A. Nourhani, E. Karshalev, F. Soto, J. Wang, Research 2020, 2020, 7823615.   
[41] R. Mohammadinejad, S. Karimi, S. Iravani, R. S. Varma, Green Chem. 2015, 18, 20.   
[42] X. Yan, J. Xu, Q. Zhou, D. Jin, C. I. Vong, Q. Feng, D. H. L. Ng, L. Bian, L. Zhang, Appl. Mater. Today 2019, 15, 242.   
[43] W. Gao, X. Feng, A. Pei,C. R. Kane, R. Tam, C. Hennessy, J. Wang, Nano Lett. 2014, 14, 305.   
[44] T-Y. Huang, M. S. Sakar, A. Mao, A.J. Petruska, F. Qiu, X-B. Chen, S. Kennedy, D. Mooney, B. J. Nelson, Adv. Mater. 2015, 27, 6644.   
[45] C. de Marco, C. C. J. Alcantara, S. Kim, F. Briatico, A. Kadioglu, G. de Bernardis, X. Chen, C. Marano, B. J. Nelson, S. Pane, Adv. Mater. Technol. 2019, 4, 1900332.   
[46] W. Zhu, J. Li, Y. J. Leong, I. Rozen, X. Qu, R. Dong, Z. Wu, W. Gao, P. H. Chung, J. Wang, S. Chen, Adv. Mater. 2015, 27, 4411.   
[47] J. M. McNeill, J. M. Braxton, N. Nama, T. E. Mallouk, ACS Nano 2020, 14, 7520.   
[48] S. Schuerle, A. P. Soleimany, T. Yeh, G. M. Anand, M. Haberli, H. E. Fleming, N. Mirkhani, F. Qiu, S. Hauert, X. Wang, B. J. Nelson, S. N. Bhatia, Sci. Adv. 2019, 5, eaav4803.   
[49] M. M. Hawkeye, M. J. Brett,J. Vac. Sci. Technol., A 2007, 25, 1317.   
[50] A. G. Mark, J. G. Gibbs, T. C. Lee, P. Fischer, Nat. Mater. 2013, 12, 802.   
[51] D. Schamel, M. Pfeifer, J. G. Gibbs, B. Miksch, A. G. Mark, P. Fischer, J. Am. Chem. Soc. 2013, 135, 12353.   
[52] H. Yuping, W. Jinsong, Z. Yiping, Nano Lett. 2007, 7, 1369.   
[53] L. Soler, V. Magdanz, V. M. Fomin, S. Sanchez, O. G. Schmidt, ACS Nano 2013, 7, 9611.   
[54] V. K. Bandari, Y. Nan, D. Karnaushenko, Y. Hong, B. Sun, F. Striggow, D. D. Karnaushenko, C. Becker, M. Faghih, M. Medina-Sanchez, F. Zhu, O. G. Schmidt, Nat. Electron. 2020, 3, 172.   
[55] O. G. Schmidt, K. Eberl, Nature 2001, 410, 168.   
[56] Z. Hosseinidoust, B. Mostaghaci, O. Yasa, B. W. Park, A. V. Singh, M. Sitti, Adv. Drug Delivery Rev. 2016, 106, 27.   
[57] I. S. M. Khalil, H. C. Dijkslag, L. Abelmann, S. Misra, Appl. Phys. Lett. 2014, 104, 223701.   
[58] V. Magdanz, J. Gebauer, P. Sharan, S. Eltoukhy, D. Voigt, J. Simmchen, Adv. Biosyst. 2019, 3, 1900061.   
[59] C. Chen, X. Chang, P. Angsantikul, J. Li, B. Esteban-Fernandez de Avila, E. Karshalev, W. Liu, F. Mou, S. He, R. Castillo, Y. Liang, J. Guan, L. Zhang, J. Wang, Adv. Biosyst. 2018, 2, 1700160.   
[60] B. Mostaghaci, O. Yasa, J. Zhuang, M. Sitti, Adv. Sci. 2017, 4, 1700058.   
[61] O. I. Senturk, O. Schauer, F. Chen, V. Sourjik, S. V. Wegner, Adv. Healthcare Mater. 2020, 9, 1900956.   
[62] W. Asghar, V. Velasco, J. L. Kingsley, M. S. Shoukat, H. Shafiee, R. M. Anchan, G. L. Mutter, E. Tuzel, U. Demirci, Adv. Healthcare Mater. 2014, 3, 1671.   
[63] S. Tasoglu, H. Safaee, X. Zhang, J. L. Kingsley, P. N. Catalano, U. A. Gurkan, A. Nureddin, E. Kayaalp, R. M. Anchan, R. L. Maas, E. Tuzel, U. Demirci, Small 2013, 9, 3374.   
[64] C. K. Tung, L. Hu, A. G. Fiore, F. Ardon, D. G. Hickman, R. O. Gilbert, S. S. Suarez, M. Wu, Proc. Natl. Acad. Sci. USA 2015, 112, 5431.   
[65] X. Zhang, I. Khimji, U. A. Gurkan, H. Safaee, P. N. Catalano, H. O. Keles, E. Kayaalp, U. Demirci, Lab Chip 2011, 11, 2535.   
[66] M. M. Quinn, L. Jalalian, S. Ribeiro, K. Ona, U. Demirci, M. I. Cedars, M. P. Rosen, Hum. Reprod. 2018, 33, 1388.   
[67] T. Chinnasamy, J. L. Kingsley, F. Inci, P. J. Turek, M. P. Rosen, B. Behr, E. Tuzel, U. Demirci, Adv. Sci. 2018, 5, 1700531.   
[68] R. Nosrati, P. J. Graham, B. Zhang, J. Riordon, A. Lagunov, T. G. Hannam, C. Escobedo, K. Jarvi, D. Sinton, Nat. Rev. Urol. 2017, 14, 707.   
[69] S. Palagi, P. Fischer, Nat. Rev. Mater. 2018, 3, 113.   
[70] P. Fischer, A. Ghosh, Nanoscale 2011, 3, 557.   
[71] P. Mandal, G. Patil, H. Kakoty, A. Ghosh, Acc. Chem. Res. 2018, 51, 2689.   
[72] X. Z. Chen, M. Hoop, F. Mushtaq, E. Siringil, C. Hu, B. J. Nelson, S. Pane, Appl. Mater. Today 2017, 9, 37.   
[73] M. Fernandez-Medina, M. A. Ramos-Docampo, O. Hovorka, V. Salgueirino, B. Stadler, Adv. Funct. Mater. 2020, 30, 1908283.   
[74] K. Yuan, Z. Jiang, B. Jurado-Sanchez, A. Escarpa, Chem. - Eur.J. 2020, 26, 2309.   
[75] L. Zhang, J. J. Abbott, L. Dong, B. E. Kratochvil, D. Bell, B. J. Nelson, Appl. Phys. Lett. 2009, 94, 064107.   
[76] D. Schamel, A. G. Mark, J. G. Gibbs, C. Miksch, K. I. Morozov, A. M. Leshansky, P. Fischer, ACS Nano 2014, 8, 8794.   
[77] P. L. Venugopalan, R. Sai, Y. Chandorkar, B. Basu, S. Shivashankar, A. Ghosh, Nano Lett. 2014, 14, 1968.   
[78] W. Gao, S. Sattayasamitsathit, K. M. Manesh, D. Weihs, J. Wang,J. Am. Chem. Soc. 2010, 132, 14403.   
[79] B. Jang, E. Gutman, N. Stucki, B. F. Seitz, P. D. Wendel-Garcia, T. Newton, J. Pokki, O. Ergeneman, S. Pane, Y. Or, B. J. Nelson, Nano Lett. 2015, 15, 4829.   
[80] T. Li, A. Zhang, G. Shao, M. Wei, B. Guo, G. Zhang, L. Li, W. Wang, Adv. Funct. Mater. 2018, 28, 1706066.   
[81] C. Bi, M. Guix, B. V. Johnson, W. Jing, D. J. Cappelleri, Micromachines 2018, 9, 68.   
[82] S. Tottori, L. Zhang, F. Qiu, K. K. Krawczyk, A. Franco-Obregon, B. J. Nelson, Adv. Mater. 2012, 24, 811.   
[83] M. Salehizadeh, E. Diller,J. Micro-Bio Rob. 2017, 12, 9.   
[84] J. Zhang, M. Salehizadeh, E. Diller, in Proc. IEEE Int. Conf. Robot. Autom., IEEE, New York 2018, pp. 123-128.   
[85] K. J. Rao, F. Li, L. Meng, H. Zheng, F. Cai, W. Wang, Small 2015, 11, 2836.   
[86] T. Xu, W. Gao, L. P. Xu, X. Zhang, S. Wang, Adv. Mater. 2017, 29, 1603250.   
[87] T. Xu, L. P. Xu, X. Zhang, Appl. Mater. Today 2017, 9, 493.   
[88] C. M. Schoellhammer, A. Schroeder, R. Maa, G. Y. Lauwers, A. Swiston, M. Zervas, R. Barman, A. M. DiCiccio, W. R. Brugge, D. G. Anderson, D. Blankschtein, R. Langer, G. Traverso, Sci. Transl. Med. 2015, 7, 310ra168.   
[89] W. Wang, L. A. Castro, M. Hoyos, T. E. Mallouk, ACS Nano 2012, 6, 6122.   
[90] F. Nadal, E. Lauga, Phys. Fluids 2014, 26, 082001.   
[91] D. Kagan, M. J. Benchimol, J. C. Claussen, E. Chuluun-Erdene, S. Esener, J. Wang, Angew. Chem., Int. Ed. 2012, 51, 7519.   
[92] D. Ahmed, M. Lu, A. Nourhani, P. E. Lammert, Z. Stratton, H. S. Muddana, V. H. Crespi, T.J. Huang, Sci. Rep. 2015, 5, 9744.   
[93] A. Aghakhani, O. Yasa, P. Wrede, M. Sitti, Proc. Natl. Acad. Sci. USA 2020, 117, 3469.   
[94] L. Ren, N. Nama, J. M. McNeill, F. Soto, Z. Yan, W. Liu, W. Wang, J. Wang, T. E. Mallouk, Sci. Adv. 2019, 5, eaax3084.   
[95] M. Safdar, S. U. Khan, J. Janis, Adv. Mater. 2018, 30, 1703660.   
[96] L. L. A. Adams, D. Lee, Y. Mei, D. A. Weitz, A. A. Solovev, Adv. Mater. Interfaces 2020, 7, 1901583.   
[97] M. Guix, A. K. Meyer, B. Koch, O. G. Schmidt, Sci. Rep. 2016, 6, 21701.   
[98] Y. Xing, M. Zhou, S. Tang, Y. Fu, X. Du, T. Xu, L. Su, Y. Wen, X.Zhang, T. Ma, Angew. Chem., Int. Ed. 2020, 59, 14368.   
[99] T. C. Lee, M. Alarcon-Correa, C. Miksch, K. Hahn, J. G. Gibbs, P. Fischer, Nano Lett. 2014, 14, 2407.   
100] A. Nourhani, V. H. Crespi, P. E. Lammert, A. Borhan, Phys. Fluids 2015, 27, 092002.   
101] J. G. Gibbs, Y.-P. Zhao, Small 2009, 5, 2304.   
102] B. V. V. S. P. Kumar, A. J. Patil, S. Mann, Nat. Chem. 2018, 10, 1154.   
103] J. Li, Z. Liu, G. Huang, Z. An, G. Chen, J. Zhang, M. Li, R. Liu, Y. Mei, NPG Asia Mater 2014, 6, e94.   
104] P. S. Schattling, M. A. Ramos-Docampo, V. Salgueirino, B. Stadler, ACS Nano 2017, 11, 3973.   
105] X. Ma, A. Jannasch, U. R. Albrecht, K. Hahn, A. Miguel-Lopez, E. Schaffer, S. Sanchez, Nano Lett. 2015, 15, 7043.   
106] S. Sengupta, M. M. Spiering, K. K. Dey, W. Duan, D. Patra, P. J. Butler, R. D. Astumian, S. J. Benkovic, A. Sen, ACS Nano 2014, 8, 2410.   
107] A. Somasundar, S. Ghosh, F. Mohajerani, L. N. Massenburg, T. Yang, P. S. Cremer, D. Velegol, A. Sen, Nat. Nanotechnol. 2019, 14, 1129.   
[1u8] K. K. vey, X. nao, B. M. ansi, w. J. menaez-Urtiz, U. M. CoraovaFigueroa, R. Golestanian, A. Sen, Nano Lett. 2015, 15, 8311.   
[109] S. Hermanova, M. Pumera, Chem. - Eur. J. 2020, https://doi.org/10. 1002/chem.202001244.   
[110] X. Arque, X. Andres, R. Mestre, B. Ciraulo, J. Ortega Arroyo, R. Quidant, T. Patino, S. Sanchez, Research 2020, 2020, 2424972.   
[111] W. Gao, R. Dong, S. Thamphiwatana, J. Li, W. Gao, L.Zhang, J. Wang, ACS Nano 2015, 9, 117.   
[112] S. Wang, X. Liu, Y. Wang, D. Xu, C. Liang, J. Guo, X. Ma, Nanoscale 2019, 11, 14099.   
[113]B. Dai, J. Wang, Z. Xiong, X. Zhan, W. Dai, C. C. Li, S. P. Feng, J. Tang, Nat. Nanotechnol. 2016, 11, 1087.   
[114] V. Sridhar, B.-W. Park, S. Guo, P. A. van Aken, M. Sitti, ACS Appl. Mater. Interfaces 2020, 12, 24149.   
[115] L. Xu, F. Mou, H. Gong, M. Luo, J. Guan, Chem. Soc. Rev. 2017, 46, 6905.   
[116] R. Dong, Y. Cai, Y. Yang, W. Gao, B. Ren, Acc. Chem. Res. 2018, 51, 1940.   
[117] X. Chen, C. Zhou, Y. Peng, Q. Wang, W. Wang, ACS Appl. Mater. Interfaces 2020, 12, 11843.   
[118] Y. Huang, Z. Liang, M. Alsoraya, J. Guo, D. (Emma) Fan, Adv. Intll. Syst. 2020, 2, 1900127.   
[119] H. P. Lee, A. K. Gaharwar, Adv. Sci. 2020, 14, 2000863.   
[120] Y. Alapan, B. Yigit, O. Beker, A. F. Demirors, M. Sitti, Nat. Mater. 2019, 18, 1244.   
[121] K. Han, C. W. Shields, O. D. Velev, Adv. Funct. Mater. 2018, 28, 1705953.   
[122] A. M. Boymelgreen, T. Balli T. Miloh, G. Yossifon, Nat. Commun. 2018, 9, 760.   
[123] G. Loget, A. Kuhn, Nat. Commun. 2011, 2, 535.   
[124] M. Sentic, G. Loget, D. Manojlovic, A. Kuhn, N. Sojic, Angew. Chem., Int. Ed. 2012, 51, 11284.   
[125] J. Roche, S. Carrara, J.Sanchez,J. Lannelongue, G. Loget, L. Bouffier, P. Fischer, A. Kuhn, Sci. Rep. 2014, 4, 6705.   
[126] H. Wang, M. Pumera, Adv. Funct. Mater. 2018, 28, 1705421.   
[127] H. Xu, M. Medina-Sanchez, O. G. Schmidt, Angew. Chem. 2020, 59, 15029.   
[128] A. Singh, M. Ansari, M. Mahajan, S. Srivastava, S. Kashyap, P. Dwivedi, V. Pandit, U. Katha, Micromachines 2020, 11, 448.   
[129] H. Xu, M. Medina-Sanchez, V. Magdanz, L. Schwarz, F. Hebenstreit, O. G. Schmidt, ACS Nano 2018, 12, 327.   
[130] F. Soto, M. A. Lopez-Ramirez, I. Jeerapan, B. Esteban-Fernandez de Avila, R. K. Mishra, X. Lu, I. Chai, C. Chen, D. Kupor, A. Nourhani, J. Wang, Adv. Funct. Mater. 2019, 29, 1900658.   
[131] S. Ryu, R. E. Pepper, M. Nagai, D. C. France, Micromachines 2017, 8, 4.   
[132] P. Ramesh, S.J. Hwang, H. C. Davis, A. Lee-Gosselin, V. Bharadwaj, M. A. English, J. Sheng, V. lyer, M. G. Shapiro, Angew. Chem., Int. Ed. 2018, 57, 12385.   
[133] M. Aubry, W-A. Wang, Y. Guyodo, E. Delacou, J. M. Guignier, O. Espeli, A. Lebreton, F. Guyot, Z. Gueroui, bioRxiv (Preprint) 2020, https://doi.org/10.1101/2020.01.06.895623.   
[134] A. Bar-Zion, A. Nourmahnad, D. R. Mittelstein, S. Yoo, D. Malounda, M. Abedi, A. Lee-Gosselin, D. Maresca, M. G. Shapiro, bioRxiv (Preprint) 2019, 620567.   
[135] M. G. Shapiro, P. W. Goodwill, A. Neogy, M. Yin, F. S. Foster, D. V. Schaffer, S. M. Conolly, Nat. Nanotechnol. 2014, 9, 311.   
[136] M. O. Din, T. Danino, A. Prindle, M. Skalak, J. Selimkhanov, K. Allen, E. Julio, E. Atolia, L. S. Tsimring, S. N. Bhatia, J. Hasty, Nature 2016, 536, 81.   
[137] M. O. Din, A. Martin, I. Razinkov, N. Csicsery, J. Hasty, Sci. Adv. 2020, 6, eaaz8344.   
[138] M. Luo, Y. Feng, T. Wang, J. Guan, Adv. Funct. Mater. 2018, 28, 1706100.   
[139] W. Gao, J. Wang, Nanoscale 2014, 6, 10486.   
[140] P. Erkoc, I. C. Yasa, H. Ceylan, O. Yasa, Y. Alapan, M. Sitti, Adv. Ther. 2019, 2, 1800064.   
[141] S. Wang, K. Liu, F. Wang, F. Peng, Y. Tu, Chem. - Asian J. 2019, 14, 2336.   
[142] K. Ulbrich, K. Hola, V. Subr, A. Bakandritsos, J. Tucek, R. Zboril, Chem. Rev. 2016, 116, 5338.   
[143] S. Mitragotri, P. A. Burke, R. Langer, Nat. Rev. Drug Discovery 2014, 13, 655.   
[144] D. Xu, Y. Wang, C. Liang, Y. You, S. Sanchez, X. Ma, Small 2019, 1902464.   
[145] A. V. Singh, M. H. D. Ansari, P. Laux, A. Luch, Expert Opin. Drug Delivery 2019, 16, 1259.   
[146] J. Wang, R. Dong, H. Wu, Y. Cai, B. Ren, Nano-Micro Lett. 2020, 12, 11.   
[147] J. Wang, W. Gao, ACS Nano 2012, 6, 5745.   
[148] R. Mundaca-Uribe, B. Esteban-Fernandez de Avila, M. Holay, P. LekShmy Venugopalan, B. Nguyen, J. Zhou, A. Abbas, R. H. Fang, L. Zhang, J. Wang, Adv. Healthcare Mater. 2020, 2000900.   
[149] J. Min, Y. Yang, Z. Wu, W. Gao, Adv. Ther. 2020, 3, 1900125.   
[150] W. Gao, D. Kagan, O. S. Pak, C. Clawson, S. Campuzano, E. ChuluunErdene, E. Shipton, E. E. Fullerton, L. Zhang, E. Lauga, J. Wang, Small 2012, 8, 460.   
[151] R. Mhanna, F. Qiu, L. Zhang, Y. Ding, K. Sugihara, M. Zenobi-Wong, B. J. Nelson, Small 2014, 10, 1953.   
[152] K. Villa, L. Krejcova, F. Novotny, Z. Heger, Z. Sofer, M. Pumera, Adv. Funct. Mater. 2018, 28, 1804343.   
[153] V. Garcia-Gradilla, J. Orozco, S. Sattayasamitsathit, F. Soto, F. Kuralay, A. Pourazary, A. Katzenberg, W. Gao, Y. Shen, J. Wang, ACS Nano 2013, 7, 9232.   
[154] B. Khezri, S. M. Beladi Mousavi, L. Krejcova, Z. Heger, Z. Sofer, M. Pumera, Adv. Funct. Mater. 2019, 29, 1806696.   
[155] S. M. Beladi-Mousavi, B. Khezri, L. Krejcova, Z. Heger, Z. Sofer, A. C. Fisher, M. Pumera, ACS Appl. Mater. Interfaces 2019, 11, 13359.   
[156] V. Garcia-Gradila, S. Sattayasamitsathit, F. Soto, F. Kuralay, C. Yardimci, D. Wiitala, M. Galarnyk, J. Wang, Small 2014, 10, 4154.   
[157] Y. Xing, M. Zhou, X. Du, X. Li, J. Li, T. Xu, X. Zhang, Appl. Mater. Today 2019, 17, 85.   
[158] M. Hoop, F. Mushtaq, C. Hurter, X. Z. Chen, B. J. Nelson, S. Pane, Nanoscale 2016, 8, 12723.   
[159] S. Fusco, H. W. Huang, K. E. Peyer, C. Peters, M. Haberli, A. Ulbers, A. Spyrogianni, E. Pellicer, J. Sort, S. E. Pratsinis, B. J. Nelson, M. S. Sakar, S. Pane, ACS Appl. Mater. Interfaces 2015, 7, 6803.   
[160] S. Fusco, G. Chatzipirpiridis, K. M. Sivaraman, O. Ergeneman, B. J. Nelson, S. Pane, Adv. Healthcare Mater. 2013, 2, 1037.   
[161] X. Xu, K. Kim, D. Fan, Angew. Chem., Int. Ed. 2015, 54, 2525.   
[162] K. Kim, J. Guo, X. Xu, D. Fan, ACS Nano 2015, 9, 548.   
[163] K. Kim, X. Xu, J. Guo, D. L. Fan, Nat. Commun. 2014, 5, 1.   
[164] A. C. Hortelao, T. Patino, A. Perez-Jimenez, A. Blanco, S. Sanchez, Adv. Funct. Mater. 2018, 28, 1705086.   
[165] S. K. Srivastava, G. Clergeaud, T. L. Andresen, A. Boisen, Adv. Drug Delivery Rev. 2019, 138, 41.   
[166] Z. Wu, Y. Wu, W. He, X. Lin, J. Sun, Q. He, Angew. Chem., Int. Ed. 2013, 52, 7000.   
[167] Z. Wu, X. Lin, X. Zou, J. Sun, Q. He, ACS Appl. Mater. Interfaces 2015, 7, 250.   
[168] Y. Wu, X. Lin, Z. Wu, H. Mohwald, Q. He, ACS Appl. Mater. Interfaces 2014, 6, 10476.   
[169] Y. Tu, F. Peng, A. A. M. Andre, Y. Men, M. Srinivas, D. A. Wilson, ACS Nano 2017, 11, 1957.   
[170] Y. Tu, F. Peng, P. B. White, D. A. Wilson, Angew. Chem. 2017, 129, 7728.   
[171] F. Mou, C. Chen, Q. Zhong, Y. Yin, H. Ma, J. Guan, ACS Appl. Mater. Interfaces 2014, 6, 9897.   
[1/4] J. Ll, r. Angsanuikul, w. Liu, D. cstevan-rermanuez ue Avna, . Inarmphiwatana, M. Xu, E. Sandraz, X. Wang, J. Delezuk, W. Gao, L. Zhang, J. Wang, Angew. Chem., Int. Ed. 2017, 56, 2156.   
[173] B. E. F. De Avila, P. Angsantikul, J.Li, M. Angel Lopez-Ramirez, D. E. Ramirez-Herrera, S. Thamphiwatana, C. Chen, J. Delezuk, R. Samakapiruk, V. Ramez, L. Zhang, J. Wang, Nat. Commun. 2017, 8, 272.   
[174] A. Paryab, H. R. Madaah Hosseini, F. Abedini, A. Dabbagh, New J. Chem. 2020, 44, 6947.   
[175] M. Zhou, T. Hou, J. Li, S. Yu, Z. Xu, M. Yin, J. Wang, X. Wang, ACS Nano 2019, 13, 1324.   
[176] K. Liu, J. Ou, S. Wang, J. Gao, L. Liu, Y. Ye, D. A. Wilson, Y. Hu, F. Peng, Y. Tu, Appl. Mater. Today 2020, 20, 100694.   
[177] E. Karshalev, B. Esteban-Fernandez De Avila, M. Beltran-Gastelum, P. Angsantikul, S. Tang, R. Mundaca-Uribe, F. Zhang, J. Zhao, L. Zhang, J. Wang, ACS Nano 2018, 12, 8397.   
[178] B. Esteban-Fernandez de Avila, M. A. Lopez-Ramirez, R. MundacaUribe, X. Wei, D. E. Ramirez-Herrera, E. Karshalev, B. Nguyen, R. H. Fang, L. Zhang, J. Wang, Adv. Mater. 2020, 32, 2000091.   
[179] J. Li, S. Thamphiwatana, W. Liu, B. Esteban-Fernandez De Avila, P. Angsantikul, E. Sandraz, J. Wang, T. Xu, F. Soto, V. Ramez, X. Wang, W. Gao, L. Zhang, J. Wang, ACS Nano 2016, 10, 9536.   
[180] J. A. Tejeda-Rodriguez, A. Nunez, F. Soto, V. Garcia-Gradilla, R. Cadena-Nava, J. Wang, R. Vazquez-Duhalt, ChemNanoMat 2019, 5, 194.   
[181] Z. Wu, T. Li, J.Li, W. Gao, T. Xu, C. Christianson, W. Gao, M. Galarnyk, Q. He, L. Zhang, J. Wang, ACS Nano 2014, 8, 12041.   
[182] C. Gao, Z. Lin, D. Wang, Z. Wu, H. Xie, Q. He, ACS Appl. Mater. Interfaces 2019, 11, 23392.   
[183] Z. Wu, B. Esteban-Fernandez De Avila, A. Martin, C. Christianson, W. Gao, S. K. Thamphiwatana, A. Escarpa, Q. He, L. Zhang, J. Wang, Nanoscale 2015, 7, 13680.   
[184] H. Wang, M. G. Potroz, J. A. Jackman, B. Khezri, T. Maric, N.-J. Cho, M. Pumera, Adv. Funct. Mater. 2017, 27, 1702338.   
[185] T. Maric, M. Z. M. Nasir, N. F. Rosli, M. Budanovic, R. D. Webster, N. Cho, M. Pumera, Adv. Funct. Mater. 2020, 30, 2000112.   
[186] S. Tang, F. Zhang, H. Gong, F. Wei, J. Zhuang, E. Karshalev, B. E.-F. de Avila, C. Huang, Z. Zhou, Z. Li, L. Yin, H. Dong, R. H. Fang, X. Zhang, L. Zhang, J. Wang, Sci. Rob. 2020, 5, eaba6137.   
[187] V. Magdanz, I. S. M. Khalil J. Simmchen, G. P. Furtado, S. Mohanty, J. Gebauer, H. Xu, A. Klingner, A. Aziz, M. Medina-Sanchez, O. G. Schmidt, S. Misra, Sci. Adv. 2020, 6, eaba5855.   
[188] M. B. Akolpoglu, N. O. Dogan, U. Bozuyuk, H. Ceylan, S. Kizilel, M. Sitti, Adv. Sci. 2020, 7, 2001256.   
[189] B. W. Park, J. Zhuang, O. Yasa, M. Sitti, ACS Nano 2017, 11, 8910.   
[190] O. Felfoul, M. Mohammadi, S. Taherkhani, D. De Lanauze, Y. Zhong Xu, D. Loghin, S. Essa, S. Jancik, D. Houle, M. Lafleur, L. Gaboury, M. Tabrizian, N. Kaou, M. Atkin, T. Vuong, G. Batist, N. Beauchemin, D. Radzioch, S. Martel, Nat. Nanotechnol. 2016, 11, 941.   
[191] S. Taherkhani, M. Mohammadi, J. Daoud, S. Martel, M. Tabrizian, ACS Nano 2014, 8, 5049.   
[192] J. Shao, M. Xuan, H. Zhang, X. Lin, Z. Wu, Q. He, Angew. Chem., Int. Ed. 2017, 56, 12935.   
[193] Z. Wang, Y. Tu, Y. Chen, F. Peng, Adv. Intell. Syst. 2020, 2, 1900081.   
[194] U. Bozuyuk, O. Yasa, I. C. Yasa, H. Ceylan, S. Kizilel, M. Sitti, ACS Nano 2018, 12, 9617.   
[195] H. Ceylan, I. C. Yasa, O. Yasa, A. F. Tabak, J. Giltinan, M. Sitti, ACS Nano 2019, 13, 3353.   
[196] Y. Alapan, U. Bozuyuk, P. Erkoc, A. C. Karacakol, M. Sitti, Sci. Rob. 2020, 5, eaba5726.   
[197] X Wang, X-H. Qin, C. Hu, A. Terzopoulou, X-Z. Chen, T-Y. Huang, K. Maniura-Weber, S. Pane, B. J. Nelson, Adv. Funct. Mater. 2018, 28, 1804107   
[3o] . wang zopoulou, C. de Marco, C. Hu, A. J. de Mello, P. Falcaro, S. Furukawa, B. J. Nelson, J. Puigmarti-Luis, S. Pane, Adv. Mater. 2019, 31, 1901592.   
[199] S. S. Andhari, R. D. Wavhale, K. D. Dhobale, B. V. Tawade, G. P. Chate, Y. N. Patil, J. J. Khandare, S. S. Banerjee, Sci. Rep. 2020, 10, 4703.   
[200] F. Mushtaq, H. Torlakcik, M. Hoop, B. Jang, F. Carlson, T. Grunow, N. Laubli, A. Ferreira, X. Chen, B. J. Nelson, S. Pane, Adv. Funct. Mater. 2019, 29, 1808135.   
[201] M. Liu, L. Pan, H. Piao, H. Sun, X. Huang, C. Peng, Y. Liu, ACS Appl. Mater. Interfaces 2015, 7, 26017.   
[202] P. Rutgeerts, S. Vermeire, G. Van Assche, Gastroenterology 2009, 136, 1182.   
[203] C. Krieckaert, T. Rispens, G. Wolbink, Curr. Opin. Rheumatol. 2012, 24, 306.   
[204] D. Fan, Z. Yin, R. Cheong, F. Q. Zhu, R. C. Cammarata, C. L. Chien, A. Levchenko, Nat. Nanotechnol. 2010, 5, 545.   
[205] P. Diez, B. Esteban-Fernandez De Avila, D. E. Ramirez-Herrera, R. Villalonga, J. Wang, Nanoscale 2017, 9, 14307.   
[206] J. R. Baylis, J. H. Yeon, M. H. Thomson, A. Kazerooni, X. Wang, A. E. S. John, E. B. Lim, D. Chien, A. Lee, J. Q. Zhang, J. M. Piret, L. S. Machan, T. F. Burke, N. J. White, C. J. Kastrup, Sci. Adv. 2015, 1, e1500379.   
[207] J. R. Baylis, A. E. St John, X. Wang, E. B. Lim, M. L. Statz, D. Chien, E. Simonson, S. A. Stern, R. T. Liggins, N. J. White, C. J. Kastrup, Shock 2016, 46, 123.   
[208] H. P. Adams, G. del Zoppo, M. J. Alberts, D. L. Bhatt, L. Brass, A. Furlan, R. L. Grubb, R. T. Higashida, E. C. Jauch, C. Kidwell, P. D. Lyden, L. B. Morgenstern, A. I. Qureshi, R. H. Rosenwasser, P. A. Scott, E. F. M. Wijdicks, Circulation 2007, 115, e478.   
[209] M. D. Kaminski, Y. Xie, C. J. Mertz, M. R. Finck, H. Chen, A. J. Rosengart, Eur.J. Pharm. Sci. 2008, 35, 96.   
[210] N. Korin, M. Kanapathipillai, B. D. Matthews, M. Crescente, A. Brill, T. Mammoto, K. Ghosh, S. Jurek, S. A. Bencherif, D. Bhatta, A. U. Coskun, C. L. Feldman, D. D. Wagner, D. E. Ingber, Science 2012, 337, 738.   
[211] R. Cheng, W. Huang, L. Huang, B. Yang, L. Mao, K. Jin, Q. Zhuge, Y. Zhao, ACS Nano 2014, 8, 7746.   
[212] J. Hu, W. Huang, S. Huang, Q. ZhuGe, K. Jin, Y. Zhao, Nano Res. 2016, 9, 2652.   
[213] J. Hu, S. Huang, L. Zhu, W. Huang, Y. Zhao, K. Jin, Q. Zhuge, ACS Appl. Mater. Interfaces 2018, 10, 32988.   
[214] M. Xie, W. Zhang, C. Fan, C. Wu, Q. Feng, J. Wu, Y. Li, R. Gao, Z. Li, Q. Wang, Y. Cheng, B. He, Adv. Mater. 2020, 32, 2000366.   
[215] X. Wei, M. Beltran-Gastelum, E. Karshalev, B. Esteban-Fernandez De Avila, J. Zhou, D. Ran, P. Angsantikul, R. H. Fang, J. Wang, L. Zhang, Nano Lett. 2019, 19, 1914.   
[216] C. Wang, B. E. Fernandez de Avila, R. Mundaca-Uribe, M. A. LopezRamirez, D. E. Ramirez-Herrera, S. Shukla, N. F. Steinmetz, J. Wang, Small 2020, 16, 1907150.   
[217] M. A. Lopez-Ramirez, F. Soto, C. Wang, R. Rueda, S. Shukla, C. SilvaLopez, D. Kupor, D. A. McBride, J. K. Pokorski, A. Nourhani, N. F. Steinmetz, N. J. Shah, J. Wang, Adv. Mater. 2020, 32, 1905740.   
[218] M. Wan, Q. Wang, R. Wang, R. Wu, T. Li, D. Fang, Y. Huang, Y. Yu, L. Fang, X. Wang, Y. Zhang, Z. Miao, B. Zhao, F. Wang, C. Mao, Q. Jiang, X. Xu, D. Shi, Sci. Adv. 2020, 6, eaaz9014.   
[219] H. Xu, M. Medina-Sanchez, M. F. Maitz, C. Werner, O. G. Schmidt, ACS Nano 2020, 14, 2982.   
[220] F. Striggow, M. Medina-Sanchez, G. K. Auernhammer, V. Magdanz, B. M. Friedrich, O. G. Schmidt, Small 2020, 16, 2000213.   
[221] M. Bao, J. Xie, W. T. S. Huck, Adv. Sci. 2018, 5, 1800448.   
[222] S. Kim, F. Qiu, S. Kim, A. Ghanbari, C. Moon, L. Zhang, B. J. Nelson, H. Choi, Adv. Mater. 2013, 25, 5863. 2019, 29, 1808992.   
[224] T. Kurinomaru, A. Inagaki, M. Hoshi, C. Nakamura, H. Yamazoe, Mater. Horiz. 2020, 7, 877.   
[225] S. Tasoglu, E. Diller, S. Guven, M. Sitti, U. Demirci, Nat. Commun. 2014, 5, 1.   
[226] T. Ren, P. Chen, L. Gu, M. G. Ogut, U. Demirci, Adv. Mater. 2020, 32, 1905713.   
[227] M. Dong, X. Wang, X. Chen, F. Mushtaq, S. Deng, C. Zhu, H. Torlakcik, A. Terzopoulou, X. Qin, X. Xiao, J. Puigmarti-Luis, H. Choi, A. P. Pego, Q. Shen, B. J. Nelson, S. Pane, Adv. Funct. Mater. 2020, 30, 1910323.   
[228] Y. Yu, J. Guo, Y. Wang, C. Shao, Y. Wang, Y. Zhao, ACS Appl. Mater. Interfaces 2020, 12, 16097.   
[229] J. i, X Li, T. Luo, R. Wang, C. Liu, S. Chen, D. Li, J. Yue, S. H. Cheng, D. Sun, Sci. Rob. 2018, 3, eaat8829.   
[230] S. Jeon, S. Kim, S. Ha, S. Lee, E. Kim, S. Y. Kim, S. H. Park, J. H. Jeon, S. W. Kim, C. Moon, B. J. Nelson, J. Young Kim, S. W. Yu, H. Choi, Sci. Rob. 2019, 4, eaav4317.   
[231] G. Go, S. G. Jeong, A. Yoo, J. Han, B. Kang, S. Kim, K. T. Nguyen, Z. Jin, C. S. Kim, Y. R. Seo, J. Y. Kang, J. Y. Na, E. K. Song, Y. Jeong, J. K. Seon, J. O. Park, E. Choi, Sci. Rob. 2020, 5, eaay6626.   
[232] F. Zhang, R. Mundaca-Uribe, H. Gong, B. Esteban-Fernandez de Avila, M. Beltran-Gastelum, E. Karshalev, A. Nourhani, Y. Tong, B. Nguyen, M. Gallot, Y. Zhang, L. Zhang, J. Wang, Adv. Mater. 2019, 31, 1901828.   
[233] Y. Alapan, O. Yasa, O. Schauer, J. Giltinan, A. F. Tabak, V. Sourjik, M. Sitti, Sci. Rob. 2018, 3, eaar4423.   
[234] C. M. J. Hu, R. H. Fang, J. Copp, B. T. Luk, L. Zhang, Nat. Nanotechnol. 2013, 8, 336.   
[235] M. Medina-Sanchez, L. Schwarz, A. K. Meyer, F. Hebenstreit, O. G. Schmidt, Nano Lett. 2016, 16, 555.   
[236] V. Magdanz, M. Medina-Sanchez, L. Schwarz, H. Xu, J. Elgeti, O. G. Schmidt, Adv. Mater. 2017, 29, 1606301.   
[237] L. Feng, M. Hagiwara, A. Ichikawa, T. Kawahara, F. Arai, in Proc. IEEE Int. Conf. Intell. Robot. Syst., IEEE, New York 2012, p. 944.   
[238] L. Feng, P. Di, F. Arai, Int. J. Rob. Res. 2016, 35, 1445.   
[239] L. Schwarz, D. D. Karnaushenko, F. Hebenstreit, R. Naumann, O. G. Schmidt, M. Medina-Sanchez, Adv. Sci. 2020, 2000843.   
[240] J. Park, C. Jin, S. Lee, J. Kim, H. Choi, Adv. Healthcare Mater. 2019, 8, 1900213.   
[241] M. Hoop, A. S. Ribeiro, D. Rosch, P. Weinand, N. Mendes, F. Mushtaq, X.-Z. Chen, Y. Shen, C. F. Pujante, J. Puigmarti-Luis, J. Paredes, B. J. Nelson, A. P. Pego, S. Pane, Adv. Funct. Mater. 2018, 28, 1705920.   
[242] P. Dhar, S. Narendren, S. S. Gaur, S. Sharma, A. Kumar, V. Katiyar, Int. J. Biol. Macromol. 2020, 158, 1020.   
[243] W. He, J. Frueh, N. Hu, L. Liu, M. Gai, Q. He, Adv. Sci. 2016, 3, 1600206.   
[244] X. Wang, J. Cai, L. Sun, S. Zhang, D. Gong, X. Li, S. Yue, L. Feng, D. Zhang, ACS Appl. Mater. Interfaces 2019, 11, 4745.   
[245] L. Xie, X. Pang, X. Yan, Q. Dai, H. Lin, J. Ye, Y. Cheng, Q. Zhao, X. Ma, X. Zhang, G. Liu, X. Chen, ACS Nano 2020, 14, 2880.   
[246] M. B. Zimmermann, R. F. Hurrell, Lancet 2007, 370, 511.   
[247] E. Karshalev, Y. Zhang, B. Esteban-Fernandez De Avila, M. BeltranGastelum, Y. Chen, R. Mundaca-Uribe, F. Zhang, B. Nguyen, Y. Tong, R. H. Fang, L. Zhang, J. Wang, Nano Lett. 2019, 19, 7816.   
[248] S. M. Beladi-Mousavi, J. Klein, B. Khezri, L. Walder, M. Pumera, ACS Nano 2020, 14, 3434.   
[249] D. E. Marx, D. J. Barillo, Burns 2014, 40, S9.   
[250] V. V. Singh, B. Jurado-Sanchez, S. Sattayasamitsathit, J. Orozco, J.Li, M. Galarnyk, Y. Fedorak, J. Wang, Adv. Funct. Mater. 2015, 25, 2147.   
[251] D. Vilela, M. M. Stanton, J. Parmar, S. Sanchez, ACS Appl. Mater. Interfaces 2017, 9, 22093. A. Petruska, M. J. Loessner, B. J. Nelson, S. Pane, Adv. Funct. Mater. 2016, 26, 1063.   
[253] F Soto, D. Kupor, M. A. Lopez-Ramirez, F, Wei, E. Karshalev, S. Tang, F. Tehrani, J. Wang, Angew. Chem., Int. Ed. 2020, 59, 3480.   
[254] B. J. Nelson, I. K. Kaliakatsos, J. J. Abbott, Annu. Rev. Biomed. Eng. 2010, 12, 55.   
[255] H. Rezaei Nejad, B. C. M. Oliveira, A. Sadeqi, A. Dehkharghani, I. Kondova, J. A. M. Langermans, J. S. Guasto, S. Tzipori, G. Widmer, S. R. Sonkusale, Adv. Intell. Syst. 2019, 1, 1900053.   
[256] J. Min, Y. Yang, Z. Wu, W. Gao, Adv. Ther. 2020, 3, 1900125.   
[257] J. C. Breger, C. Yoon, R. Xiao, H. R. Kwag, M. O. Wang, J. P. Fisher, T. D. Nguyen, D. H. Gracias, ACS Appl. Mater. Interfaces 2015, 7, 3398.   
[258] E. Gultepe, J. S. Randhawa, S. Kadam, S. Yamanaka, F. M. Selaru, E. J. Shin, A. N. Kalloo, D. H. Gracias, Adv. Mater. 2013, 25, 514.   
[259] S. Fusco, M. S. Sakar, S. Kennedy, C. Peters, R. Bottani, F. Starsich, A. Mao, G. A. Sotiriou, S. Pane, S. E. Pratsinis, D. Mooney, B. J. Nelson, Adv. Mater. 2014, 26, 952.   
[260] Q. Jin, Y. Yang, J. Jackson, C. Yoon, D. H. Gracias, Nano Lett. 2020, 20, 5383.   
[261] N. Bassik, A. Brafman, A. M. Zarafshar, M. Jamal, D. Luvsanjav, F. M. Selaru, D. H. Gracias,J. Am. Chem. Soc. 2010, 132, 16314.   
[262] A. Ghosh, Y. Liu, D. Artemov, D. H. Gracias, Adv. Healthcare Mater. 2020, 2000869.   
[263] R. Di Giacomo, S. Krodel, B. Maresca, P. Benzoni, R. Rusconi, R. Stocker, C. Daraio, Sci. Rep. 2017, 7, 1.   
[264] Y. Zhang, Y. Liu, H. Liu, W. H. Tang, Cell Biosci. 2019, 9, 1.   
[265] M. O. Ozen, K. Sridhar, M. G. Ogut, A. Shanmugam, A. S. Avadhani, Y. Kobayashi, J. C. Wu, F. Haddad, U. Demirci, Biosens. Bioelectron. 2020, 150, 111930.   
[266] F. Liu, O. Vermesh, V. Mani, T. J. Ge, S. J. Madsen, A. Sabour, E.- C. Hsu, G. Gowrishankar, M. Kanada, J. V. Jokerst, R. G. Sierra, E. Chang, K. Lau, K. Sridhar, A. Bermudez, S. J. Pitteri, T. Stoyanova, R. Sinclair, V. S. Nair, S. S. Gambhir, U. Demirci, ACS Nano 2017, 11, 10712.   
[267] R. Mayeux, NeuroRx 2004, 1, 182.   
[268] Y. Y. Broza, X. Zhou, M. Yuan, D. Qu, Y. Zheng, R. Vishinkin, M. Khatib, W. Wu, H. Haick, Chem. Rev. 2019, 119, 11761.   
[269] A. Abramson, E. Caffarel-Salvador, M. Khang, D. Dellal, D. SilverStein, Y. Gao, M. R. Frederiksen, A. Vegge, F. Hubalek, J. J. Water, A. V. Friderichsen, J. Fels, R. K. Kirk, C. Cleveland, J. Collins, S. Tamang, A. Hayward, T. Landh, S. T. Buckley, N. Roxhed, U. Rahbek, R. Langer, G. Traverso, Science 2019, 363, 611.   
[270] A. Abramson, E. Caffarel-Salvador, V. Soares, D. Minahan, R. Y. Tian, X. Lu, D. Dellal, Y. Gao, S. Kim, J. Wainer, J. Collins, S. Tamang, A. Hayward, T. Yoshitake, H. C. Lee, J. Fujimoto, J. Fels, M. R. Frederiksen, U. Rahbek, N. Roxhed, R. Langer, G. Traverso, Nat. Med. 2019, 25, 1512.   
[271] S. Jafari, L. O. Mair, I. N. Weinberg, J. Baker-McKee, O. Hale, J. Watson-Daniels, B. English, P. Y. Stepanov, C. Ropp, O. F. Atoyebi, D. Sun, J. Magn. Magn. Mater. 2019, 469, 302.   
[272] L. O. Mair, i. N. Weinberg, D. N. Teceno, S. Jafari, D. Sun, P. Stepanov, O. Hale, C. Ropp, F. M. Vassoler, in Int. IEEE/EMBS Conf. Neural Eng. NER, IEEE Computer Society, San Francisco, CA 2019, p. 977.   
[273] W. Xi, A. A. Solovev, A. N. Ananth, D. H. Gracias, S. Sanchez, O. G. Schmidt, Nanoscale 2013, 5, 1294.   
[274] D. Walker, B. T. Kasdorf, H. H. Jeong, O. Lieleg, P. Fischer, Sci. Adv. 2015, 1, e1500501.   
[275] F. Ullrich, C. Bergeles, J. Pokki, O. Ergeneman, S. Erni, G. Chatzipirpiridis, S. Pane, C. Framme, B. J. Nelson, Invest. Ophthalmol. Visual Sci. 2013, 54, 2853. Pellicer, S. A. Pot, B. M. Spiess, S. Pane, B. J. Nelson, J. Biomed. Mater. Res., Part B 2017, 105, 836.   
[277] Z. Wu, J. Toll, H. H. Jeong, Q. Wei, M. Stang, F. Ziemssen, Z. Wang, M. Dong, S. Schnichels, T. Qiu, P. Fischer, Sci. Adv. 2018, 4, eaat4388.   
[278] F. Soto, A. Martin, S. Ibsen, M. Vaidyanathan, V. Garcia-Gradilla, Y. Levin, A. Escarpa, S. C. Esener, J. Wang, ACS Nano 2016, 10, 1522.   
[279] F. Soto, I. Jeerapan, C. Silva-Lopez, M. A. Lopez-Ramirez, I. Chai, L. Xiaolong, J. Ly, J F. Kurniawan, I. Martin, K. Chakravarthy, J. Wang, Small 2018, 14, 1803266.   
[280] J. J. Kwan, R. Myers, C. M. Coviello, S. M. Graham, A. R. Shah, E. Stride, R. C. Carlisle, C. C. Coussios, Small 2015, 11, 5305.   
[281] R. G. Thomas, U. S. Jonnalagadda, J. J. Kwan, Langmuir 2019, 35, 10106.   
[282] R. Myers, C. Coviello, P. Erbs, J. Foloppe, C. Rowe, J. Kwan, C. Crake, S. Finn, E. Jackson, J. M. Balloul, C. Story, C. Coussios, R. Carlisle, Mol. Ther. 2016, 24, 1627.   
[283] P. Zhang, G. Wu, C. Zhao, L. Zhou, X. Wang, S. Wei, Colloids Surf, B 2020, 194, 111204.   
[284] D. Wang, C. Gao, C. Zhou, Z. Lin, Q. He, Research 2020, 2020, 3676954.   
[285] J. Vyskocil, C. C. Mayorga-Martinez, E. Jablonska, F. Novotny, T. Ruml, M. Pumera, M. Pumera, M. Pumera, M. Pumera, ACS Nano 2020, 14, 8247.   
[286] W. Wang, Z. Wu, Q. He, View 2020, 20200005.   
[287] P. L. Venugopalan, B. Esteban-Fernandez de Avila, M. Pal, A. Ghosh, J. Wang, ACS Nano 2020, https://doi.org/10.1021/acsnano. Oc05217.   
[288] W. Wang, S. Li, L. Mair, S. Ahmed, T. J. Huang, T. E. Mallouk, Angew. Chem., Int. Ed. 2014, 53, 3201.   
[289] B. Esteban-Fernandez De Avila, A. Martin, F. Soto, M. A. LopezRamirez, S. Campuzano, G. M. Vasquez-Machado, W. Gao, L. Zhang, J. Wang, ACS Nano 2015, 9, 6756.   
[290] B. Esteban-Fernandez De Avila, C. Angell, F. Soto, M. A. LopezRamirez, D. F. Baez, S. Xie, J. Wang, Y. Chen, ACS Nano 2016, 10, 4997.   
[291] B. Esteban-Fernandez De Avila, D. E. Ramirez-Herrera, S. Campuzano, P. Angsantikul, L. Zhang, J. Wang, ACS Nano 2017, 11, 5367.   
[292] F. Zhang, J. Zhuang, B. Esteban Fernandez De Avila, S. Tang, Q. Zhang, R. H. Fang, L. Zhang, J. Wang, ACS Nano 2019, 13, 11996.   
[293] M. Hansen-Bruhn, B. E. F. de Avila, M. Beltran-Gastelum, J. Zhao, D. E. Ramirez-Herrera, P. Angsantikul, K. Vesterager Gothelf, L. Zhang, J. Wang, Angew. Chem., Int. Ed. 2018, 57, 2657.   
[294] F. Soto, G. L. Wagner, V. Garcia-Gradilla, K. T. Gillespie, D. R. Lakshmipathy, E. Karshalev, C. Angell, Y. Chen, J. Wang, Nanoscale 2016, 8, 17788.   
[295] W. Wang, Z. Wu, X. Lin, T. Si, Q. He, J. Am. Chem. Soc. 2019, 141, 6601.   
[296] A. Llopis-Lorente, A. Garcia-Fernandez, N. Murillo-Cremaes, A. C. Hortelao, T. Patino, R. Villalonga, F. Sancenon, R. Martinez-Manez, S. Sanchez, ACS Nano 2019, 13, 12171.   
[297] X. Jiao, Z. Wang, J. Xiu, W. Dai, L. Zhao, T. Xu, X. Du, Y. Wen, X. Zhang, Appl. Mater. Today 2020, 18, 100504.   
[298] S. K. Srivastava, M. Medina-Sanchez, B. Koch, O. G. Schmidt, Adv. Mater. 2016, 28, 832.   
[299] S. Lee, J. Kim, J. Kim, A. K. Hoshiar, J. Park, S. Lee, J. Kim, S. Pane, B. J. Nelson, H. Choi, Adv. Healthcare Mater. 2020, 9, 2070019.   
[300] X. Xu, S. Hou, N. Wattanatorn, F. Wang, Q. Yang, C. Zhao, X. Yu, H. R. Tseng, S. J. Jonas, P. S. Weiss, ACS Nano 2018, 12, 4503.   
[301] M. Sun, Q. Liu, X. Fan, Y. Wang, W. Chen, C. Tian, L. Sun, H. Xie, Small 2020, 16, 1906701.   
[302] F. Qiu, S. Fujita, R. Mhanna, L. Zhang, B. R. Simona, B. J. Nelson, Adv. Funct. Mater. 2015, 25, 1666. G. Gutierrez, P. Fischer, Adv. Mater. 2020, 32, 2001114.   
[304] Y. Wang, Y. Liu, Y. Li, D. Xu, X. Pan, Y. Chen, D. Zhou, B. Wang, H. Feng, X. Ma, Research 2020, 2020, 7962024.   
[305] J. A. Jernigan, K. M. Hatfield, H. Wolford, R. E. Nelson, B. Olubajo, S. C. Reddy, N. McCarthy, P. Paul, L. C. McDonald, A. Kallen, A. Fiore, M. Craig, J. Baggs, N. Engl. J. Med. 2020, 382, 1309.   
[306] S. Rigo, C. Cai, G. Gunkel-Grabole, L. Maurizi, X. Zhang, J. Xu, C. G. Palivan, Adv. Sci. 2018, 5, 1700892.   
[307] L. O. Mair, A. Nacev, R. Hilaman, P. Y. Stepanov, S. Chowdhury, S. Jafari, J. Hausfeld, A. J. Karlsson, M. E. Shirtliff, B. Shapiro, I. N. Weinberg, J. Magn. Magn. Mater. 2017, 427, 81.   
[308] M. M. Stanton, B. W. Park, D. Vilela, K. Bente, D. Faivre, M. Sitti, S. Sanchez, ACS Nano 2017, 11, 9968.   
[309] A. C. Hortelao, R. Carrascosa, N. Murillo-Cremaes, T. Patino, S. Sanchez, ACS Nano 2019, 13, 429.   
[310] G. Hwang, A. J. Paula, E. E. Hunter, Y. Liu, A. Babeer, B. Karabucak, K. Stebe, V. Kumar, E. Steager, H. Koo, Sci. Rob. 2019, 4, eaaw2388.   
[311] K. Kim, J. Guo, Z. Liang, D. Fan, Adv. Funct. Mater. 2018, 28, 1705867.   
[312] T. Patino, A. Porchetta, A. Jannasch, A. Llado, T. Stumpp, E. Schaffer, F. Ricci, S. Sanchez, Nano Lett. 2019, 19, 3440.   
[313] J.Wu, S. Balasubramanian, D. Kagan, K. M. Manesh, S. Campuzano, J. Wang, Nat. Commun. 2010, 1, 36.   
[314] D. Kagan, S. Campuzano, S. Balasubramanian, F. Kuralay, G. U. Flechsig, J. Wang, Nano Lett. 2011, 11, 2083.   
[315] J. Simmchen, A. Baeza, D. Ruiz, M. J. Esplandiu, M. Vallet-Regi, Small 2012, 8, 2053.   
[316] M. S. Draz, K. M. Kochehbyoki, A. Vasan, D. Battalapalli, A. Sreeram, M. K. Kanakasabapathy, S. Kallakuri, A. Tsibris, D. R. Kuritzkes, H. Shafiee, Nat. Commun. 2018, 9, 4282.   
[317] K. Van Nguyen, S. D. Minteer, Chem. Commun. 2015, 51, 4782.   
[318] S. Fu, X. Zhang, Y. Xie, J. Wu, H. Ju, Nanoscale 2017, 9, 9026.   
[319] Y. Xie, S. Fu, J. Wu, J. Lei, H. Ju, Biosens. Bioelectron. 2017, 87, 31.   
[320] . Zhang, C. Chen, J. Wu, H. Ju, ACS Appl. Mater. Interfaces 2019, 11, 13581.   
[321] J. Orozco, S. Campuzano, D. Kagan, M. Zhou, W. Gao, J. Wang, Anal. Chem. 2011, 83, 7962.   
[322] M. Luo, Y. Jiang, J. Su, Z. Deng, F. Mou, L. Xu, J. Guan, Chem. -Asian J. 2019, 14, 2503.   
[323] F. Kuralay, S. Sattayasamitsathit, W. Gao, A. Uygun, A. Katzenberg, J. Wang, J. Am. Chem. Soc. 2012, 134, 15217.   
[324] C. Huang, X. Shen, Chem. Commun. 2014, 50, 2646.   
[325] D. Vilela, J. Orozco, G. Cheng, S. Sattayasamitsathit, M. Galarnyk, C. Kan, J. Wang, A. Escarpa, Lab Chip 2014, 14, 3505.   
[326] L. Restrepo-Perez, L. Soler, C. Martinez-Cisneros, S. Sanchez, O. G. Schmidt, Lab Chip 2014, 14, 2914.   
[327] M. S. Draz, N. K. Lakshminaraasimulu, S. Krishnakumar, D. BattaIapalli, A. Vasan, M. K. Kanakasabapathy, A. Sreeram, S. Kallakuri, P. Thirumalaraju, Y. Li, S. Hua, X. G. Yu, D. R. Kuritzkes, H. Shafiee, ACS Nano 2018, 12, 5709.   
[328] B. Esteban-Fernandez De Avila, M. A. Lopez-Ramirez, D. F. Baez, A. Jodra, V. V. Singh, K. Kaufmann, J. Wang, ACS Sens. 2016, 1, 217.   
[329] A. Molinero-Fernandez, M. Moreno-Guzman, M. A. Lopez, A. Escarpa, Anal. Chem. 2017, 89, 10850.   
[330] B. Jurado-Sanchez, M. Pacheco, J. Rojo, A. Escarpa, Angew. Chem., Int. Ed. 2017, 56, 6957.   
[331] Y. Zhang, L. Zhang, L. Yang, C. I. Vong, K. F. Chan, W. K. K. Wu, T. N. Y. Kwong, N. W. S. Lo, M. Ip, S. H. Wong, J. J. Y. Sung, P. W. Y. Chiu, L. Zhang, Sci. Adv. 2019, 5, eaau9650.   
[332] I. Jung, S. Ih, H. Yoo, S. Hong, S. Park, Nano Lett. 2018, 18, 1984.   
[333] X. Yu, Y. Li, J. Wu, H. Ju, Anal. Chem. 2014, 86, 4501.   
[334] E. Morales-Narvaez, M. Guix, M. Medina-Sanchez, C. C. MayorgaMartinez, A. Merkogi, Small 2014, 10, 2542.   
[335] M. Pacheco, B. Jurado-Sanchez, A. Escarpa, Anal. Chem. 2018, 90, 2912.   
[336] B. E. F. de Avila, M. Zhao, S. Campuzano, F. Ricci, J. M. Pingarron, M. Mascini, J. Wang, Talanta 2017, 167, 651.   
[337] L. Kong, N. Rohaizad, M. Z. M. Nasir, J. Guan, M. Pumera, Anal. Chem. 2019, 91, 5660.   
[338] A. I. Bunea, I. A. Pavel, S. David, S. Gaspar, Biosens. Bioelectron. 2015, 67, 42.   
[339] M. Li, H. Zhang, M. Liu, B. Dong, J. Mater. Chem. C 2017, 5, 4400.   
[340] S. M. Russell, A. Alba-Patino, M. Borges, R. de Ia Rica, Biosens. Bioelectron. 2019, 140, 111346.   
[341] R. Maria-Hormigos, B. Jurado-Sanchez, A. Escarpa, Adv. Funct. Mater. 2018, 28, 1704256.   
[342] S. R. Weinberger, T. S. Morris, M. Pawlak, Pharmacogenomics 2000, 1, 395.   
[343] S. S. Banerjee, A. Jalota-Badhwar, K. R. Zope, K. J. Todkar, R. R. Mascarenhas, G. P. Chate, G. V. Khutale, A. Bharde, M. Calderon, J. J. Khandare, Nanoscale 2015, 7, 8684.   
[344] A. Chatupniak, E. Morales-Narvaez, Adv. Drug Delivery Rev. 2015, 95, 104.   
[345] S. Campuzano, J. Orozco, D. Kagan, M. Guix, W. Gao, S. Sattayasamitsathit, J. C. Claussen, A. Merkogi, J. Wang, Nano Lett. 2012, 12, 396.   
[346] M. Garcia, J. Orozco, M. Guix, W. Gao, S. Sattayasamitsathit, A. Escarpa, A. Merkogi, J. Wang, Nanoscale 2013, 5, 1325.   
[347] J. Orozco, G. Pan, S. Sattayasamitsathit, M. Galarnyk, J. Wang, Analyst 2015, 140, 1421.   
[348] J. A. M. Delezuk, D. E. Ramirez-Herrera, B. Esteban-Fernandez de Avila, J. Wang, Nanoscale 2017, 9, 2195.   
[349] S. Balasubramanian, D. Kagan, C. M. Jack Hu, S. Campuzano, M. J. Lobo-Castanon, N. Lim, D, Y Kang, M. Zimmerman, L. Zhang, J. Wang, Angew. Chem., Int. Ed. 2011, 50, 4161.   
[350] S. Sanchez, A. A. Solovev, S. Schulze, O. G. Schmidt, Chem. Commun. 2011, 47, 698.   
[351] S. Lee, S. Kim, S. Kim, J. Y. Kim, C. Moon, B. J. Nelson, H. Choi, Adv. Healthcare Mater. 2018, 7, 1700985.   
[352] Z. Lin, X. Fan, M. Sun, C. Gao, Q. He, H. Xie, ACS Nano 2018, 12, 2539.   
[353] N. F. Laubli, N. Shamsudhin, H. Vogler, G. Munglani, U. Grossniklaus, D. Ahmed, B. J. Nelson, Small Methods 2019, 3, 1800527.   
[354] X. Lu, K. Zhao, W. Liu, D. Yang, H. Shen, H. Peng, X. Guo, J. Li, J. Wang, ACS Nano 2019, 13, 11443.   
[355] Z. Wu, T. Li, W. Gao, T. Xu, B. Jurado-Sanchez, J. Li, W. Gao, Q. He, L. Zhang, J. Wang, Adv. Funct. Mater. 2015, 25, 3881.   
[356] Z. Wu, J. Li, B. E.-F. de Avila, T Li, W. Gao, Q. He, L. Zhang, J. Wang, Adv. Funct. Mater. 2015, 25, 7497.   
[357] J. Li, P. Angsantikul, W. Liu, B. Esteban-Fernandez de Avila, X. Chang, E. Sandraz, Y. Liang, S. Zhu, Y. Zhang, C. Chen, W. Gao, L. Zhang, J. Wang, Adv. Mater. 2018, 30, 1704800.   
[358] B. E. F. De Avila, P. Angsantikul, D. E. Ramirez-Herrera, F. Soto, H. Teymourian, D. Dehaini, Y. Chen, L. Zhang, J. Wang, Sci. Rob. 2018, 3, eaat0485.   
[359] M. Pal, N. Somalwar, A. Singh, R. Bhat, S. M. Eswarappa, D. K. Saini, A. Ghosh, Adv. Mater. 2018, 30, 1800429.   
[360] M. Pal, D. Dasgupta, N. Somalwar, R. Vr, M. Tiwari, D. Teja, S. M. Narayana, A. Katke, J. Rs, R. Bhat, D. K. Saini, A. Ghosh, J. Phys.: Condens. Matter 2020, 32, 224001.   
[361] D. Dasgupta, D. Pally, D. K. Saini, R. Bhat, A. Ghosh, ChemRxiv (Preprint) 2020, https://doi.org/10.26434/chemrxiv. 12063420.v1.   
[362] L. Liu, B. Chen, K. Liu, J. Gao, Y. Ye, Z. Wang, N. Qin, D. A. Wilson, Y. Tu, F. Peng, Adv. Funct. Mater. 2020, 30, 1910108.   
[363] Y. Wu, A. Fu, G. Yossifon, Small 2020, 16, 1906682.   
[364] H. H. Jeong, A. G. Mark, T. C. Lee, M. Alarcon-Correa, S. Eslami, T. Qiu, J. G. Gibbs, P. Fischer, Nano Lett. 2016, 16, 4887.   
[365] L. Wang, T. Li, L. Li, J. Wang, W. Song, G. Zhang, ECSJ. Solid State Sci. Technol. 2015, 4, S3020.   
[366] A. Zizzari, M. Cesaria, M. Bianco, L. L. del Mercato, M. Carraro, M. Bonchio, R. Rella, V. Arima, Chem. Eng. J. 2019, 123572.   
[367] I. Schoen, B. L. Pruitt, V. Vogel, Annu. Rev. Mater. Res. 2013, 43, 589.   
[368] V. Vogel, M. Sheetz, Nat. Rev. Mol. Cell Biol. 2006, 7, 265.   
[369] S. Schuerle, I. A. Vizcarra, J. Moeller, M. S. Sakar, B. Ozkale, A. M. H. Lindo, F. Mushtaq, I. Schoen, S. Pane, V. Vogel, B. J. Nelson, Sci. Rob. 2017, 2, eaah6094.   
[370] S. Pane, J. Puigmarti-Luis, C. Bergeles, X. Chen, E. Pellicer, J. Sort, V. Pocepcova, A. Ferreira, B. J. Nelson, Adv. Mater. Technol. 2019, 4, 1800575.   
[371] B. Wang, Y. Zhang, L. Zhang, Quant. Imaging Med. Surg. 2018, 8, 461.   
[372] M. Alarcon-Correa, D. Walker, T. Qiu, P. Fischer, Eur. Phys. J.: Spec. Top. 2016, 225, 2241.   
[373] C. Gao, Y. Wang, Z. Ye, Z. Lin, X. Ma, Q. He, Adv. Mater. 2020, 2000512.   
[374] G. T. van Moolenbroek, T. Patino, J. Llop, S. Sanchez, Adv. Intell. Syst. 2020, 2000087.   
[375] G. Chatzipirpiridis, O. Ergeneman, J. Pokki, F. Ullrich, S. Fusco, J. A. Ortega, K. M. Sivaraman, B. J. Nelson, S. Pane, Adv. Healthcare Mater. 2015, 4, 209.   
[376] A. Servant, F. Qiu, M. Mazza, K. Kostarelos, B. J. Nelson, Adv. Mater. 2015, 27, 2981.   
[377] X. Yan, Q. Zhou, M. Vincent, Y. Deng, J. Yu, J. Xu, T. Xu, T. Tang, L. Bian, Y. X. J. Wang, K. Kostarelos, L. Zhang, Sci. Rob. 2017, 2, eaaq1155.   
[378] D. Akin, J. Sturgis, K. Ragheb, D. Sherman, K. Burkholder, J. P. Robinson, A. K. Bhunia, S. Mohammed, R. Bashir, Nat. Nanotechnol. 2007, 2, 441.   
[379] S. J. Park, S. H. Park, S. Cho, D. M. Kim, Y. Lee, S. Y. Ko, Y. Hong, H. E. Choy, J. J. Min, J. O. Park, S. Park, Sci. Rep. 2013, 3, 1.   
[380] A. Aziz, M. Medina-Sanchez, N. Koukourakis, J. Wang, R. Kuschmierz, H. Radner, J. W. Czarske, O. G. Schmidt, Adv. Funct. Mater. 2019, 29, 1905272.   
[381] D. Li, M. Jeong, E. Oren, T. Yu, T. Qiu, Robotics 2019, 8, 87.   
[382] E. S. Olson, J. Orozco, Z. Wu, C. D. Malone, B. Yi, W. Gao, M. Eghtedari, J. Wang, R. F. Mattrey, Biomaterials 2013, 34, 8918.   
[383] Z. Wu, L. Li, Y. Yang, P. Hu, Y. Li, S. Y. Yang, L. V. Wang, W. Gao, Sci. Rob. 2019, 4, eaax0613.   
[384] O. Felfoul, A. T. Becker, G. Fagogenis, P. E. Dupont, Sci. Rep. 2016, 6, 33567.   
[385] E. A. Perigo, G. Hemery, O. Sandre, D. Ortega, E. Garaio, F. Plazaola, F. J. Teran, Appl. Phys. Rev. 2015, 2, 041302.   
[386] D. Vilela, U. Cossio, J. Parmar, A. M. Martinez-Villacorta, V. GomezVallejo, J. Llop, S. Sanchez, ACS Nano 2018, 12, 1220.   
[387] V. lacovacci, A. Blanc, H. Huang, L. Ricotti, R. Schibli, A. Menciassi, M. Behe, S. Pane, B. J. Nelson, Small 2019, 15, 1900709.   
[388] P. B. Nguyen, B. Kang, D. M. Bappy, E. Choi, S. Park, S. Y. Ko, J. O. Park, C. S. Kim, Int. J. Comput. Assist. Radiol. Surg. 2018, 13, 1843.   
[389] Z. Xu, M. Chen, H. Lee, S. P. Feng, J. Y. Park, S. Lee, J. T. Kim, ACS Appl. Mater. Interfaces 2019, 11, 15727.   
[390] I. C. Yasa, H. Ceylan, U. Bozuyuk, A.-M. Wild, M. Sitti, Sci. Rob. 2020, 5, eaaz3867.   
[391] C. Chen, E. Karshalev, J. Li, F. Soto, R. Castillo, I. Campos, F. Mou, J. Guan, J. Wang, ACS Nano 2016, 10, 10389.   
[392] I. A. B. Pijpers, S. Cao, A. Llopis-Lorente, J. Zhu, S. Song, R. R. M. Joosten, F. Meng, H. Friedrich, D. S. Williams, S. Sanchez, J. C. M. van Hest, L. K. E. A. Abdelmohsen, Nano Lett. 2020, 20, 4472.   
[393] Z. Wu, Y. Chen, D. Mukasa, O. S. Pak, W. Gao, Chem. Soc. Rev. 2020, 49, 2869.   
[394] V. lacovacci, L. Ricotti, E. Sinibaldi, G. Signore, F. Vistoli, A. Menciassi, Adv. Sci. 2018, 5, 1800807.   
[395] V. lacovacci, L. Ricotti, G. Signore, F. Vistoli, E. Sinibaldi, A. Menciassi, in Proc. IEEE Int. Conf. Robot. Autom., IEEE, New York 2019, p. 2495.   
[396] C. Li, C. Guo, V. Fitzpatrick, A. Ibrahim, M. J. Zwierstra, P. Hanna, A. Lechtig, A. Nazarian, S. J. Lin, D. L. Kaplan, Nat. Rev. Mater. 2020, 5, 61.   
[397] C. F. Guimaraes, L. Gasperini, A. P. Marques, R. L. Reis, Nat. Rev. Mater. 2020, 5, 351.   
[398] R. L. Truby, J. A. Lewis, Nature 2016, 540, 371.   
[399] F. Xu, C.A. M. Wu, V. Rengarajan, T. D. Finley, H. O. Keles, Y. Sung, B. Li, U. A. Gurkan, U. Demirci, Adv. Mater. 2011, 23, 4254.   
[400] S. Tasoglu, C. H. Yu, H. I. Gungordu, S. Guven, T. Vural, U. Demirci, Nat. Commun. 2014, 5, 1.   
[401] S. Tasoglu, D. Kavaz, U. A. Gurkan, S. Guven, P. Chen, R. Zheng, U. Demirci, Adv. Mater. 2013, 25, 1137.   
[402] A. Tocchio, N. G. Durmus, K. Sridhar, V. Mani, B. Coskun, R. EI Assal, U. Demirci, Adv. Mater. 2018, 30, 1705034.   
[403] N. G. Durmus, H. C. Tekin, S. Guven, K. Sridhar, A. A. Yildiz, G. Calibasi, I. Ghiran, R. W. Davis, L. M. Steinmetz, U. Demirci, Proc. Natl. Acad. Sci. USA 2015, 112, E3661.   
[404] S. Tasoglu, C. H. Yu, V. Liaudanskaya, S. Guven, C. Migliaresi, U. Demirci, Adv. Healthcare Mater. 2015, 4, 1469.   
[405] V. A. Parfenov, Y. D. Khesuani, S. V. Petrov, P. A. Karalkin, E. V. Koudan, E. K. Nezhurina, F. Das Pereira, A. A. Krokhmal, A. A. Gryadunova, E. A. Bulanova, I. V. Vakhrushev, I. I. Babichenko, V. Kasyanov, O. F. Petrov, M. M. Vasiliev, K. Brakke, S. I. Belousov, T. E. Grigoriev, E. O. Osidak, E. I. Rossiyskaya, L. B. Buravkova, O. D. Kononenko, U. Demirci, V. A. Mironov, Sci. Adv. 2020, 6, eaba4174.   
[406] P. Chen, Z. Luo, S. Guven, S. Tasoglu, A. V. Ganesan, A. Weng, U. Demirci, Adv. Mater. 2014, 26, 5936.   
[407] P. Chen, P. Chen, H. Wu, S. Lee, A. Sharma, D. A. Hu, S. Venkatraman, A. V. Ganesan, O. B. Usta, M. Yarmush, F. Yang, J. C. Wu, U. Demirci, S. M. Wu, Biomaterials 2017, 131, 47.   
[408] C. Bouyer, P. Chen, S. Guven, T. T. Demirtas, T. J. F. Nieland, F. Padilla, U. Demirci, Adv. Mater. 2016, 28, 161.   
[409] P. Chen, S. Guven, O. B. Usta, M. L. Yarmush, U. Demirci, Adv. Healthcare Mater. 2015, 4, 1937.   
[410] P. S. Knoepfler, Adv. Drug Delivery Rev. 2015, 82-83, 192.   
[411] M. A. Hamburg, Science 2012, 336, 299.   
[412] P. Mirowski, R. Van Horn, Soc. Stud. Sci. 2005, 35, 503.   
[413] T. Li, X. Chang, Z. Wu, J. Li, G. Shao, X. Deng, J. Qiu, B. Guo, G. Zhang, Q. He, L. Li, J. Wang, ACS Nano 2017, 11, 9268.   
[414] E. Maine, P. Seegopaul, Nat. Mater. 2016, 15, 487.   
[415] A. J. Stevens, Nat. Biotechnol. 2017, 35, 608.   
[416] L. M. McNamee, M. J. Walsh, F. D. Ledley, PLoS One 2017, 12, e0177371.   
[417] L. M. Mcnamee, F. D. Ledley, Nat. Biotechnol. 2012, 30, 937.

![](img/73aac5fff61bcf7b8c7e4b7646b7ae2f03496a5f6b6c61352da32e2dddbea38e.jpg)

Fernando Soto received his Ph.D. degree in nanoengineering from the University of California, San Diego, where he worked on developing micro/nanorobots and medical devices toward biomedical applications. He is currently a postdoctoral researcher atthe radiology department at Stanford University where he is interested in performing interdisciplinary research atthe interface between nanotechnology and bioengineering.

![](img/f5279e1af002bf8f9170930dc572a4e19ceba111903cb64b25160f2707ae5e7e.jpg)

Jie Wang is a postdoctoral researcher at Stanford University School of Medicine, with a research focus in developing micro/nanomaterials technologies. She has expertise in microfabrication, chemistry, material science with a focus on biomedical engineering applications. She has been focused on developing micromotor fabrication and engineering technology to isolate and detect biotargets from whole blood, as well as engineered the biocompatible micromotors as a drug delivery carrier for tumor diagnostic and therapy.

![](img/a525a8a731129b51613ba9b6d586d4ff6586d18fb228f3170e6453fca11aafd4.jpg)

Rajib Ahmed is currently working as a postdoctoral fellow at Stanford University School of Medicine, Canary Center at Stanford for Cancer Early Detection. He received his B.Sc. and M.Sc. degrees from the University of Dhaka in 2010 and 2012, and also studied a double degree M.Sc. in Aston University and Technische Universitat Berlin in 2013-2014. He received his Ph.D. degree from the University of Birmingham in 2018. His current research focuses on micro- and nanotechnologies-based biomedcal optical devices for virus and cancer detection.

![](img/ccce15fe42f701043759761404c527f6718d757481bf7f11ea7fbafc28edd88d.jpg)

Utkan Demirci is a professor with tenure at Stanford University School of Medicine and serves as the co-director at the Canary Center for Cancer Early Detection ofthe Department of Radiology. His group focuses on developing innovative microfluidic biomedical technology platforms with broad applications to multiple diseases. Some ofhis inventions have already been translated into FDA approved products serving patients. He has mentored and trained many successful scientists, entrepreneurs, and academicians.