# Di<PERSON><PERSON>'s and <PERSON>-<PERSON> in Finding the Shortest Path: a Tutorial

Ade Candra   
Department of Computer Science   
Faculty of Computer Science and   
Information Technology   
Universitas Sumatra Utara   
Medan, Indonesia   
<EMAIL>   
<PERSON> of Computer Science   
Faculty of Computer Science and   
Information Technology   
Universitas Sumatra Utara   
Medan, Indonesia   
<EMAIL>   
<PERSON> Hart<PERSON>o   
Department of Computer Science   
Faculty of Computer Science and   
Information Technology   
Universitas Sumatra Utara   
Medan, Indonesia   
<EMAIL>

Abstract--As one form of the greedy algorithm, Dijkstra's can handle the shortest path search with optimum result in longer search time. <PERSON>jk<PERSON>'s is contrary to A-Star, a best-first search algorithm, which can handle the shortest path search with a faster time but not always optimum. By looking at the advantages and disadvantages of <PERSON><PERSON><PERSON>'s and A-Star, this tutorial discusses the implementation of the two algorithms in finding the shortest path in routes selection between 24 SPBU (gas stations). The routes are located in Medan City and represented in a directed graph. Moreover, the authors compare <PERSON><PERSON><PERSON>'s and A-star based on the complexity of Big-Theta (O) and running time. The results show that the shortest path search between SPBU can be solved with <PERSON><PERSON><PERSON>'s and A-Star, where in some cases, the routes produced by the two algorithms are different so that the total distance generated is also different. In this case, the running time of A-Star is proven to be faster than <PERSON><PERSON><PERSON>'s, and it is following A-Star principle which selects the location point based on the best heuristic value while Dijkstra's does not. For the complexity, Dijkstra's is $\Theta ( \mathbf { n } ^ { 2 } )$ and A-Star is $\mathbf { \Theta } \mathbf { e } ( \mathbf { m } ^ { * } \mathbf { n } )$ , where $\mathbf { 0 } \leq \mathbf { m } \leq \mathbf { n }$

Keywords $\mathbf { - } A$ Star, Dijkstra's, Big-Theta, running time, SPBU

# 1. INTRODUCTION

number of nodes determines the speed of finding a solution. Dijkstra's can handle the shortest path with optimum result, but the time spent searching for the shortest path is longer.

On the other hand, $\mathbf { A } ^ { * }$ algorithm was first described by Peter Hart, Nils Nilsson, and Bertram Raphael in 1968 with a heuristic method [6]. $\mathbf { A } ^ { * }$ algorithm is one of the many search algorithms that take input, evaluates some possible paths, and return the solution. $\mathbf { A } ^ { * }$ is widely used in pathfinding and graph transversal, the process of plotting an efficiently traversable path between points [7]. $\mathbf { A } ^ { * }$ is a best-first search algorithm by modifying the heuristic function. This algorithm minimizes the total cost of the path, and in the right conditions will provide the best solution in optimal time. $\mathrm { ~ A ~ } ^ { * }$ can choose the shortest path in a faster time, but it is not always optimum.

By looking at the advantages and disadvantages of Dijkstra's and $\mathbf { A } ^ { * }$ , the authors apply both algorithms and analyze the performance in finding the shortest path based on Big-Theta (O) and running time.

The shortest path problem is the problem of finding a path that will be passed from one point to another with minimum or optimal value. In graph theory, object of the problem is represented as a node (vertice) and the relationship between the objects is represented by a line (edge) that connecting the nodes [1]. The shortest path search has been applied in various fields to optimize the performance of a system, either to minimize the costs or to speed up the process. In this tutorial, the authors discuss the implementation of Dijkstra's and $\mathbf { A } ^ { * }$ (A-Star) algorithms in generating the optimal solution in the route's' selection between 24 SPBU (gas  stations). Furthermore, the authors compare the performance of both algorithms based on Big-Theta (O) and running time. The routes are represented as a weighted graph that has a value or weight and all weights are assumed to be positive. In this case study, the edge-weighted graph represents the distance between the locations of SPBU in Medan City.

Dijkstra's is one of the most popular shortest path algorithms formulated by computer scientist Edsger W. Dijkstra's in 1956 and published in 1959. Dijkstra's algorithm solves the single-source shortest path problem for a graph with non-negative edge path costs [2]. If the graph contains negative edges, the path cannot be passed and may give incorrect results [3]. This algorithm chooses the closest point which is the smallest weight [4], and these weights are the value of each edge [5]. As a form of the greedy algorithm, Dijkstra's processes each node on the graph once, and the

# II. METHODS

This section will discuss how Dijkstra's and $\mathbf { A } ^ { * }$ solve the shortest path problem and define their performance.

# A. Dijkstra's

Dijkstra's works by assigning some initial distance values and trying to improve them step by step, as explained below.

1)  Assign to every node a tentative distance value: set it to zero for the initial node and to infinity for all other nodes (undefined). 2) Mark all nodes "unvisited" and initial node as "start." 3)  Consider all of its unvisited neighbors and calculate their tentative distances through the "start" node. Compare the newly calculated tentative distance to the "start" node value and assign the smaller one. 4)  When all neighbors already visited from the "start" node, mark these nodes as "visited." A "visited" node will never be rechecked and last-saved tentative distance is the smallest value.

5) Set the "unvisited" node that is marked with the smallest tentative distance as the new "start" and go back to step 3.

For a better understanding, the authors explain how Dijkstra's finds the shortest path from the example. As shown in Fig. 1, initial node $= \mathbf { A }$ and goal node $= \mathrm { E }$

![](img/33909605c65ec58a00eb5a83cb1523a5ea706684659192e797e0e49f06632fdb.jpg)  
Fig. 1. Graph for Dijkstra's Algorithm

Step 1:

-Create a table for result distance, with i as iteration, u as unvisited node, v as visited node, c as current node, as shown in Table 1. $\dot { \mathbf { 1 } } = 0$ is initial condition. $\mathbf { A } \mathbf { t } \dot { \mathbf { 1 } } = 1$ $\mathbf { A B } = 2$ $\mathrm { A C } = 1 0$ AB is the smallest tentative distance with cost $= 2$   
Then, set B as "start" node for $\dot { 1 } = 2$ .

TABLE I. ITERATION O DAN 1   

<html><body><table><tr><td>i</td><td>u</td><td>c</td><td>A</td><td>B</td><td>c</td><td>D</td><td>E</td></tr><tr><td>0</td><td>ABCDE</td><td>-</td><td>0,-</td><td>8,-</td><td>8,-</td><td>80,-</td><td>8,-</td></tr><tr><td>1</td><td>BCDE</td><td>A</td><td>0,-</td><td>2,A</td><td>10,A</td><td>8,-</td><td>8,-</td></tr></table></body></html>

Step 2:

At $\dot { 1 } = 2$ $\mathrm { B C } = 2 { + } 8 = 1 0$ $\mathrm { B D } = 2 { + } 8 = 1 0$ , as shown in Table 2.

-The current node C [10,B] is the same as the previous node C [10,A], so result node C can be [10,B] or [10,A].

-Then, set C as "start" node for $\dot { 1 } = 3$

TABLE II. ITERATION 2   

<html><body><table><tr><td>-</td><td>u</td><td>c</td><td>A</td><td>B</td><td>c</td><td>D</td><td>E</td></tr><tr><td>0</td><td>ABCDE</td><td>-</td><td>0,-</td><td>8,-</td><td>8,-</td><td>8,-</td><td>80,-</td></tr><tr><td>1</td><td>BCDE</td><td>A</td><td>0,-</td><td>2,A</td><td>10,A</td><td>8,-</td><td>8,-</td></tr><tr><td>2</td><td>CDE</td><td>B</td><td>0,-</td><td>2,A</td><td>10,B</td><td>10,B</td><td>8,-</td></tr></table></body></html>

Step 3:

At $\dot { 1 } = 3$ $\mathrm { C D } = 1 0 + 5 = 1 5$ $\mathrm { C E } = 1 0 + 1 0 = 2 0 $ ; as shown in Table 3.

-At current node D [15,C] is greater than the previous node D [10,B], so its value will be change to [10,B] from [15,C].

Then, set D as "start" node for $\dot { 1 } = 4$

TABLE III. ITERATION 3   

<html><body><table><tr><td>i</td><td>u</td><td>c</td><td>A</td><td>B</td><td>c</td><td>D</td><td>E</td></tr><tr><td>0</td><td>ABCDE</td><td>-</td><td>0,-</td><td>8,-</td><td>8,-</td><td>8,-</td><td>8,-</td></tr><tr><td>1</td><td>BCDE</td><td>A</td><td>0,-</td><td>2,A</td><td>10,A</td><td>8,-</td><td>8,-</td></tr><tr><td>2</td><td>CDE</td><td>B</td><td>0,-</td><td>2,A</td><td>10,B</td><td>10,B</td><td>8,-</td></tr><tr><td>3</td><td>DE</td><td>c</td><td>0,-</td><td>2,A</td><td>10,B</td><td>10,B</td><td>20,C</td></tr></table></body></html>

# Step 4:

At $\dot { 1 } = 4$ $\mathrm { D E } = 1 0 { + } 8 = 1 8$ ; as shown in Table 4.   
-The current node E [20,C] is greater than E [18,D], so the value is unchanged [18,D].   
Then, set E cannot be a "start" node because it is a "goal" node.   
Finish.

TABLE IV. ITERATION 4   

<html><body><table><tr><td>i</td><td>u</td><td>c</td><td>A</td><td>B</td><td>c</td><td>D</td><td>E</td></tr><tr><td>0</td><td>ABCDE</td><td>-</td><td>0,-</td><td>8,-</td><td>8,-</td><td>8,-</td><td>8,-</td></tr><tr><td>1</td><td>BCDE</td><td>A</td><td>0,-</td><td>2,A</td><td>10,A</td><td>8,-</td><td>8-</td></tr><tr><td>2</td><td>CDE</td><td>B</td><td>0,-</td><td>2,A</td><td>10,B</td><td>10,B</td><td>8,-</td></tr><tr><td>3</td><td>DE</td><td>c</td><td>0,-</td><td>2,A</td><td>10,B</td><td>10,B</td><td>20,C</td></tr><tr><td>4</td><td>E</td><td>D</td><td>0,-</td><td>2,A</td><td>10,B</td><td>10,B</td><td>18,D</td></tr></table></body></html>

Therefore, the resulting path in Dijkstra's is A-B-D-E, as shown in Fig.2.

![](img/2063bf66dc88843923775709aab950d2fce68fa995e686ff561b4869d8892c0a.jpg)  
Fig. 2. The resulting path for Dijkstra's

# B. A\*

A good heuristic function provides approximate costs that are close to actual costs. One of the heuristic functions that can be used in the shortest path problem is Euclidean Distance. $\mathbf { A } ^ { * }$ use Euclidean Distance and evaluates nodes by combining ${ \bf g ( n ) }$ and $\mathrm { h } ( \mathrm { n } )$ as:

$$
\mathrm { f ( n ) = g ( n ) + h ( n ) }
$$

where:

$( { \mathfrak { n } } ) =$ evaluation costs ${ \bf g } \left( { \bf n } \right) = { \bf \Omega }$ cost that already incurred from initial state to n state h $( { \mathfrak { n } } ) =$ estimated cost to arrive at a destination from n state $\mathbf { A } ^ { * }$ work step by step as explained below.

1) Start 2) Set the "Start" node as a successor. 3) Calculate all evaluation cost which is connected from the successor. 4) Do the testing: if the prospective successor is the goal, $i t$ will stop, and if not, then proceed to the next step. 5) Determine the new successor from the best evaluation cost (minimum cost) and alphabet order (if there are two evaluation costs have the same value) from previous successors.

6) Moreover, go back to step 4.

For a better understanding, the authors explain how $\mathbf { A } ^ { * }$ finds the shortest path from the example. As shown on Fig. 3, initial node $= \mathbf { A }$ and goal node $= \mathrm { E }$ . Each node has coordinates, namely: A(0,0), B(1.5,2.5), C(0,5), D(4,4), E(4,6).

![](img/890c55712b08694f4e26b54439caf343af0e85570cca6e195bbf402cb4d0d04d.jpg)  
Fig. 3.Graph for $\mathbf { A } ^ { * }$ Algorithm

![](img/55d03a0699f62d00dcb087957a7dc9ca37c5207c369ac16e73a348c79d61d76d.jpg)  
Fig. 5. Graph for step 2 in $\mathbf { A } ^ { * }$

Step 1:

Step 3:

$$
\begin{array} { l } { { h ( B E ) = \sqrt { ( x _ { E } - x _ { B } ) ^ { 2 } + ( y _ { E } + y _ { B } ) ^ { 2 } } } } \\ { { h ( B E ) = \sqrt { ( 4 - 1 . 5 ) ^ { 2 } + ( 6 - 2 . 5 ) ^ { 2 } } } } \\ { { h ( B E ) = \sqrt { ( 2 . 5 ) ^ { 2 } + ( 3 . 5 ) ^ { 2 } } } } \\ { { h ( B E ) = \sqrt { 6 . 2 5 + 1 2 . 2 5 } } } \\ { { h ( B E ) = \sqrt { 1 8 . 5 } } } \\ { { h ( B E ) = 4 . 3 } } \end{array}
$$

$$
h ( C E ) = 4 . 1 2
$$

$$
\begin{array} { l } { { h ( C E ) = \sqrt { ( x _ { E } - x _ { C } ) ^ { 2 } + ( y _ { E } + y _ { C } ) ^ { 2 } } } } \\ { { h ( C E ) = \sqrt { ( 4 - 0 ) ^ { 2 } + ( 6 - 5 ) ^ { 2 } } } } \\ { { h ( C E ) = \sqrt { 4 ^ { 2 } + 1 ^ { 2 } } } } \\ { { h ( C E ) = \sqrt { 1 6 + 1 } } } \\ { { h ( C E ) = \sqrt { 1 7 } } } \\ { { h ( C E ) = 4 . 1 2 } } \end{array}
$$

![](img/48638e63ff9854ac17190c335449e8e873983de590bfbd4caf492b68980cb150.jpg)  
Fig. 6. Graph for step 3 in A\*

![](img/ea6e5e0ee08dc522435a4f738403cf61c034ff7fd2cd5a78102fbc3997063607.jpg)  
Fig. 4. Graph for step 1 in $\mathbf { A } ^ { * }$

Therefore, the resulting path in $\mathbf { A } ^ { * }$ is A-B-D-E.

Step 2:

$$
\begin{array} { l r } { { h ( C E ) = 4 . 1 2 } } \\ { { } } \\ { { h ( D E ) = \sqrt { ( x _ { E } - x _ { D } ) ^ { 2 } + ( y _ { E } + y _ { D } ) ^ { 2 } } } } \\ { { } } \\ { { h ( D E ) = \sqrt { ( 4 - 4 ) ^ { 2 } + ( 6 - 4 ) ^ { 2 } } } } \end{array}
$$

![](img/cde4af4d47b787b7aa6d625a573434c78466ae09bf7adbcdd8c8657c7f4c15f6.jpg)  
Fig. 7. The resulting path for $\mathbf { A } ^ { * }$

# C.Asymptotic time complexity

Asymptotic notation is a notation that concern with the running time of the algorithm to explain the asymptotic time complexity. Asymptotic time complexity is an analysis of the asymptotic efficiency of an algorithm to determine the appropriate time complexity.

This research uses the $\Theta$ (Big-Theta) since it is more accurate than the O (Big-Oh) and the $\Omega$ (Big-Omega) notation. The $\Theta$ (Big-Theta) is used to categorize an algorithm into functions that describe the tight bound, which can be given the upper bound and lower bound of the function growth when the input of the function increases.

The function of $\operatorname { t } ( \mathfrak { n } )$ is defined in $\Theta ( \mathbf { g } ( \mathbf { n } ) )$ and denoted by $\mathfrak { t } ( { \mathfrak { n } } ) \in \Theta ( \mathfrak { g } ( { \mathfrak { n } } ) )$ . The $\operatorname { t } ( \mathfrak { n } )$ function is given the upper and the lower limits by several multiples of positive constants of $\mathrm { { g } ( n ) }$ for all large values of n if there are some positive constants (c1 and $\mathtt { c } _ { 2 }$ ) and some non-negative integers ${ \bf n } _ { 0 }$ [8], so that

Moreover, the distance and running time of Dijkstra's and $\mathbf { A } ^ { * }$ for different paths can be seen in Table 5 and Table 6.

TABLE V. THE RUNNING TIME OF DIJKSTRA'S   

<html><body><table><tr><td>No.</td><td>Path</td><td>Total Distance (km)</td><td>Running Time (ms)</td></tr><tr><td>1.</td><td>C-X</td><td>13.9</td><td>7.5111</td></tr><tr><td>2.</td><td>VE</td><td>23.3</td><td>7.9772</td></tr><tr><td>3.</td><td>D-A</td><td>21.9</td><td>7.777</td></tr><tr><td>4.</td><td>G-W</td><td>20.8</td><td>7.0161</td></tr><tr><td>5.</td><td>AK</td><td>18.8</td><td>8.3112</td></tr><tr><td colspan="3">Average</td><td>7.71852</td></tr></table></body></html>

TABLE VI. THE RUNNING TIME OF A\*   

<html><body><table><tr><td>No.</td><td>Path</td><td>Total Distance (km)</td><td>Running Time (ms)</td></tr><tr><td>1.</td><td>c-X</td><td>13.9</td><td>4.1866</td></tr><tr><td>2.</td><td>V-E</td><td>23.3</td><td>5.0898</td></tr><tr><td>3.</td><td>D-A</td><td>21.9</td><td>4.2552</td></tr><tr><td>4.</td><td>G-W</td><td>20.8</td><td>4.1213</td></tr><tr><td>5.</td><td>A-K</td><td>18.8</td><td>4.3774</td></tr><tr><td colspan="3">Average</td><td>4.40606</td></tr></table></body></html>

$$
c _ { 2 } g ( n ) \leq t ( n ) \leq c _ { 1 } g ( n )
$$

for all $n \geq n _ { 0 }$

# III. RESULT AND DISCUSSIONS

The computation was performed on PC Windows 8.1 Enterprise 64-bit (6.3, Build 9600), Intel(R) Core(TM) i5- 5200 CPU $_ { \textcircled { a } 2 . 2 0 \mathrm { G H z } }$ (4 CPUs), and RAM 10240 MB. Users can choose the source SPBU, the goal SPBU, and the desired algorithm from the interface, as shown in Fig. 8 and Fig.9.

![](img/fb33d1641eb3c1e05f0afbdb6a5db5fd849abace5fa96359be4d0e1927b8e410.jpg)  
Fig. 8. The path, distance, and running time with Dijkstra's

![](img/14b8e46ffc5529c5eaa37d204ec409644ce524ad728678459b4cad5c263fdc63.jpg)  
Fig. 9. The path, distance, and running time with A\*

Besides, the path and running time for Dijkstra's and $\mathbf { A } ^ { * }$ can been seen in Fig. 10 and Fig.11. Meanwhile, the map for the graph can be seen in Fig.12.

![](img/33d58260e7afefabf1a61620765a31e69b752532fbc79e003cebda61feb936f7.jpg)  
Fig. 10. Path and running time of Dijkstra's

![](img/f3565a717772d75344cb71485b2374ea1252a5b071f1100483305a6f76acdd21.jpg)  
Fig. 11. Path and running time of A\*

![](img/ce488f460ac9db004d3f069fb6bacb80a047332c137b14fbd69e8201e478e499.jpg)  
L. Anany, Introduction to The Design and Analysis of Algorithms. India: Person Education, 2009.   
Fig. 12. Map and the graph

The computation result indicates that the shortest path search between SPBU can be completed with Dijkstra's and $\mathbf { A } ^ { * }$ . For performance, the running time of A-Star is faster than Dijkstra's and it is related with $\mathbf { A } ^ { * }$ principle which selects the location point based on the best heuristic value while Dijkstra's does not. For the complexity, Dijkstra's is $\Theta ( \mathbf { n } ^ { 2 } )$ and A-Star is $\Theta ( \mathrm { m } ^ { * } \mathrm { n } )$ , where $0 \leq \mathfrak { m } \leq \mathfrak { n }$ $\Theta ( \mathbf { n } ^ { 2 } )$ means there is a nested loop with n as the variable involved. $\Theta ( \mathbf { m } ^ { * } \mathbf { n } )$ means there is a nested loop with m as the variable involved in the first loop and n as the variable involved in the second loop. Since m and n are free variables depend on the user, the complexity is considered equivalent.

# IV. CONCLUSIONS

The experiments show that Dijkstra's and $\mathbf { A } ^ { * }$ can solve the shortest path problem. However, in some cases, the routes produced by the two algorithms are different so that the total distance generated is also different. Furthermore, the running time of $\mathbf { A } ^ { * }$ is faster than Dijkstra's where the average running time of Dijkstra's is 7.719 milliseconds and $\mathbf { A } ^ { * }$ is 4.406 milliseconds. It is in line with $\mathbf { A } ^ { * }$ principle that selects the location point based on the best heuristic value. Finally, the complexity of algorithm for Dijkstra's and $\mathbf { A } ^ { * }$ is considered equivalent.

# REFERENCES

[1] T. Sutojo, E. Mulyanto, and V. Suhartono, Kecerdasan Buatan. Andi: Yogyakarta, 2011.   
[2] B. Popa and D. Popescu, "Analysis of Algorithmic for Shortest Path Problem in Parallel," $1 7 ^ { \mathrm { t h } }$ International Carpathian Control Conference (ICCC), pp. 613-617, 2016.   
[3] A. Laaksonen, "Competitive Programmer's Handbook. Draft," 2017.   
[4] V. Pandey, S.C. Yadav, and P. Arora, "Retiming Technique for Clock Period Minimization using Shortest Path Algorithm," International Conference on Computing, Communication and Automation (ICCCA), pp. 1418-1423, 2016.   
[5] J. C. D. Cruz, G. V. Magwili, J. P. E. Mundo, G. P. B. Gregorio, M. L. L. Lamoca, and J.A. Villasenor, "Items-mapping and Route Optimization in a Grocery Store using Dijkstra's, Bellman-Ford and Floyd-Warshall Algorithms," Proceedings of the International Conference IEEE Region 10 Conference (TENCON), pp. 243-246, 2016.   
[6] P. E. Hart, N. J. Nilsson, and B. Raphael, "A Formal Basis for the Heuristic Determination of Minimum Cost Paths," in IEEE Transactions on Systems Science and Cybernetics, vol. 4, no.2, pp. 100-107, 1968.   
[7] H. Reddy, "Path Finding-Dijkstra's and $\mathbf { A } ^ { * }$ Algorithm's," available at http://cs.indstate.edu/hgopireddy/newalg.html, 2013.