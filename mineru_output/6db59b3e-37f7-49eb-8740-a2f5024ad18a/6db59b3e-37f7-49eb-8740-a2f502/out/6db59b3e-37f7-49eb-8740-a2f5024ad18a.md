# Nanotechnology in biorobotics: Opportunities and challenges

Article  in  Journal of Nanoparticle Research $\cdot$ February 2015   
DOI: 10.1007/s11051-014-2792-5

# 2 authors:

# Nanotechnology in biorobotics: opportunities and challenges

<PERSON> $\cdot$ Arianna Menciassi

Received: 1 September 2014 / Accepted: 2 December 2014   
$©$ Springer Science+Business Media Dordrecht 2015

Abstract Nanotechnology recently opened a series of unexpected technological opportunities that drove the emergence of novel scientific and technological fields, which have the potential to dramatically change the lives of millions of citizens. Some of these opportunities have been already caught by researchers working in the different fields related to biorobotics, while other exciting possibilities still lie on the horizon. This article highlights how nanotechnology applications recently impacted the development of advanced solutions for actuation and sensing and the achievement of microrobots, nanorobots, and nonconventional larger robotic systems. The open challenges are described, together with the most promising research avenues involving nanotechnology.

Keywords Nanotechnology - Biorobotics - Microrobotics $\cdot$ Nanorobotics $\cdot$ Nanoactuators - Nanosensors $\cdot$ Nanostructured devices and systems

# Introduction

Biorobotics is a highly interdisciplinary scientific and technological area. Its first definitions, in the midnineties, highlighted the problems of modeling and simulating biological systems in order to provide a better understanding of human physiology (<PERSON><PERSON> et al. 1996). In this vision, robotics was conceptually associated to biological systems and considered a ‘‘metaphor’’ of them, as well as an important bridge between humans and artificial intelligence-based devices. The concept of biorobotics then further evolved, by assuming a broader connotation. Recently, it has been defined an area ‘‘which derives its methodological background mainly from the sectors or robotics and biomedical engineering, but boasts its cultural and application scope towards many sectors of engineering, towards basic and applied science … and even towards humanities’’ (Dario 2005). This vision allows to embrace many scientific and technological challenges, at the front-edge of several disciplines. Consequently, it also raises important concerns from an ethical point of view, thus fostering the discussion about techno-ethics (dealing with the ethical implications of technology) and roboethics (a branch of tecno-ethics, dealing specifically with robotic systems) (Veruggio and Operto 2008). Since nanotechnology will likely bring unpredictable new capabilities to these fields, a new sub-discipline, named nano-ethics, has recently emerged, aiming at contributing to a humanitarian and conscientious approach to nanotechnology development (Berne 2004; Grunwald 2005).

The main goals of biorobotics can be identified as follows (Dario et al. 2008): (1) to develop advanced mechatronic systems with enhanced performances and usability; (2) to advance the understanding of certain mechanisms and behaviors, typical of animals (included humans), by using robotic artifacts; (3) to achieve effective and safe interactions (such as new low invasive therapies) with biological systems. To achieve these goals, nanotechnology represents nowadays an important allied, which opens unprecedented opportunities.

At present, the integration of nanotechnology in biorobotic systems is heading mainly in two directions: on one hand, micro- and nanoscale components are more and more integrated and blended with traditional mechatronic architectures, with the aim of providing additional functionalities; on the other hand, there is a growing exploration of completely new designs and concepts for biorobotic devices and platforms, often driven by the unique properties of nanostructured materials and by the interfaces with bio-assemblies. Both trends are promising, in view of a partial revolution of mechatronic components and robotic systems at different length scales.

In particular, nanorobots are considered among the most promising systems, especially for biological and medical applications. Nanorobots can be defined as intelligent systems with overall dimensions at or below the micrometer range, made of assemblies of nanoscale components (Ferreira and Martel 2014). It is widely recognized that truly functional medical nanorobots would have a dramatic impact on healthcare, but several issues still have to be addressed, before successfully developing and assembling the different functional elements a nanorobot must be composed of. Such difficulties are mainly due to the physical laws that drive the interactions at the nanoscale. Besides a dramatic scaling of volume effects (associated with inertia, weight, heat capacity, etc.), which are dominated by surface effects (associated with friction, heat transfer, and adhesion forces), intramolecular and interatomic chemical bonding forces have an increasing role, by reducing the size, as well as quantum effects. This raises the need of developing advanced assembly and manipulation strategies, for achieving the desired objectives (Dong and Nelson 2007).

In this article, we analyze different sub-fields in which nanotechnology is currently making the difference, for the development of advanced biorobotic systems, and which show promises for future evolutions. We follow a typical schematization used to approach the hardware parts of mechatronic devices, by separately analyzing the two main building blocks of a complex system, namely the actuation module and the sensing module and by describing how nanotechnology applications are entering the design process and the evolution of each block. In addition, we discuss the main challenges and future perspectives related to the inclusion of nanotechnology within micro- and nanorobots, but also within larger biorobotic systems.

# Nanotechnology for actuation

Actuation is a key function of any machine, allowing its locomotion and, more in general, its interaction with the surrounding environment.

An interesting approach to nano-actuation is represented by artificial molecular machines. The development of such structures is necessarily based on chemical strategies for synthesizing and manipulating them (Balzani et al. 2000). Both single molecules or supramolecular systems can be achieved and controlled by remote stimuli, such as light (e.g., producing molecule isomerization). However, they show several limitations, as identified in (Requicha 2003). Some of them are the following: (i) such machines only exist in solution and can be hardly addressed individually; (ii) back-and-forth movements or continuous rotations without any mechanical coupling with a load are not very useful; (iii) the yield of an operation is usually very low. A recent report, besides defining a roadmap (in which molecular switches are excluded, because they cannot produce usable work), highlights that organized assemblies of molecular machines, integrated with metal–organic frameworks (Fig. 1a), can be applied as nanorobotic arms (Coskun et al. 2012). In addition, it points out that new types of enzyme-like mimics lie on the horizon. Even if promising for their dimensions, these machines are able to exert small forces, well below $1 ~ \mu \mathrm { N }$ (Badjic´ et al. 2004).

Biomotors are other nanoscale actuators, usually in the range of tens of nanometers. From the first attempts of attaching an F1-ATPase molecule to a surface to produce detectable forces (Montemagno and Bachand 1999) and of using a motor protein (kinesin) to produce motion (Dennis et al. 1999), some steps forward have been moved in this field. Rudimentary DNA walkers able to run along self-assembled tracks and even DNA motors that move autonomously, obtaining energy by catalyzing the reaction of DNA or RNA fuels, have been reported (Bath and Turberfield 2007; Liedl et al. 2007). More recently, origami DNA compliant structures have been also developed (Fig. 1b), demonstrating their applicability as actuation means (Rajendran et al. 2012; Zhou et al. 2013). DNA-based motors (but also other biomotors, such as those based on actin and myosin strands) show several issues: they are based on soft materials with a limited durability, they operate in a narrow range of temperature and $\mathrm { p H }$ conditions and they are rather hard to be controlled.

![](img/17cfd5b3cc76d5b1b21db0b721404850e4de1e0450a0e3d8ccd4377b7b945922.jpg)  
Fig. 1 Examples of nanoscale actuation strategies. a Possible collective phenomena in artificial molecular machines in synergy with metal–organic framework scaffolds. Switching of one molecule causes the neighboring molecules to switch, thus creating a domino-like effect propagating as a reactionfront (top images). Composite structures can be fabricated (bottom images), composed of cubes containing rigid struts and artificial molecular machines (represented by spheres) located at their midpoints. Adapted with permission from (Coskun et al. 2012). Copyright (2012): Royal Society of Chemistry. b Representation of a DNA origami compliant nanostructure. Reproduced with permission from (Zhou et al. 2013). Copyright   
(2013): American Chemical Society. c Ion-transfer actuation principle for a nanocomposite based on poly(ethylene oxide) (PEO) and single-walled carbon nanotubes (SWCNT). IL stands for ‘‘ionic liquid’’. Reproduced with permission from (Terasawa et al. 2014). Copyright (2014): Elsevier. d Representation of a flagellated bacterium attached via biotin-streptavidin link to a polystyrene (PS) microbead. Reproduced with permission from (Park et al. 2013). Copyright (2013): Nature Publishing Group. e Example of microdevice actuated by mouse skeletal muscle cells. Adapted from (Sakar et al. 2012). Copyright (2012): Royal Society of Chemistry

Nanoparticles have been largely envisioned for actuation at the nanoscale (Raguse et al. 2003). They started to show their promises 10–15 years ago, when carbon nanotubes (CNTs) were used as responsive elements enabling actuation (Baughman et al. 1999; Fennimore et al. 2003). Nowadays, nanoparticles and nanocomposites start to show good performances, especially when conjugated with smart materials (Terasawa et al. 2014; Ye and Sitti 2014) (Fig. 1c). Carbon nanotubes and other graphene-derived materials show the greatest potential in this area.

Actuation for nanoscale devices can be also assured by means of remote forces. In the case of magnetic fields, Not only magnetic nanoparticles but also magnetized living elements have been used, e.g., under magnetic resonance imaging (MRI) control (Martel et al. 2009a). The characteristics of magnetic materials are important for controlling their navigation. The key property is magnetic susceptibility $( \chi )$ , defined as the ratio of the induced magnetization (M) to the applied magnetic field (H). Nanoparticles with high $\chi$ are for example iron oxide $\mathrm { ( F e _ { 3 } O _ { 4 } ) }$ magnetic nanoparticles, also in their oxidized form, called maghemite $\left( \gamma { - } \mathrm { F e } _ { 2 } \mathrm { O } _ { 3 } \right)$ . The typical domain size for these structures is $1 5 { - } 8 0 ~ \mathrm { n m }$ (Hergt et al. 2008), thus allowing the magnetic actuation of significantly small elements (Wahajuddin and Arora 2012; Fusco et al. 2013). Neodymium-iron-boron powders have been also used to develop highly miniaturized magnetic robots, even if the typical domains of these powders are much larger (Tasoglu et al. 2014).

Bio-hybrid microrobots (or micro-biorobots) can be defined as devices integrating both artificial components and living elements (cells or tissues), aiming at exploiting the unique features of living matter within an integrated system at small scales. In this framework, whole living cells, such as flagellated bacteria or sperm cells, have been employed for providing micro/nanoscale motility to miniaturized robots, either combining them to magnetic nanoparticles, powders or layers (Sitti 2009; Magdanz et al. 2013;Ye and Sitti 2014) or using bacterial cells showing own magnetic properties, such as magnetotactic bacteria, characterized by organelles called magnetosomes that contain magnetic crystals (Martel et al. 2006; Martel et al. 2009b). Flagellated bacterial cells have been also employed without any external magnetic control, by only relying on chemotactic guidance (Fig. 1d) (Park et al. 2013). Bio-hybrid inert microrobots based on engineered monocytes have been also proposed (Park et al. 2014): although if tested only in vitro up to now, these systems should be able to exploit the chemotactic activity and transmigrating motility of monocytes in order to bring drugs toward the desired targets. However, the use of chemotaxis as the only targeting strategy would imply a rather limited therapeutic efficiency, since chemotactic gradients can be only detected at really small distances. This approach would significantly increase its potential if associated with a ‘‘pre-navigation’’ of the microrobot through a correct pathway, by exploiting other technological strategies, thus allowing a closer proximity to the target.

An emerging research trend is centered on the engineering and use of mammalian or insect whole muscle cells or tissues to power microdevices (Fig. 1e) (Xi et al. 2005; Sakar et al. 2012; Ricotti and Menciassi 2012; Chan et al. 2014). Although characterized by larger dimensions (from tens to hundreds of microns) in comparison with systems actuated by means of other technologies, this paradigm is promising and requires several nanotechnology-related efforts to support and drive cell adhesion, differentiation and long-term stability.

Within this paradigm, nanoparticles can have an important role, e.g., as intracellular transducers to maximize muscle contractility (Ricotti et al. 2013, 2014). Several nanotechnological tools will be needed to advance this field, especially if complex biorobots, constituted by multiple cell types (heterotypic cell clusters) will be addressed (Kamm and Bashir 2014).

Concerning nanoelectromechanical systems (NEMS) for actuation, most of them are resonant devices, in which the nanomechanical element is excited in one of its resonant modes (Ekinci 2005). Recently, interesting examples of NEMS for optomechanics, able to control the optical bistability in photonic crystal cavities (Tiang et al. 2013) and NEMS driven by optical gradient forces (Cai et al. 2012) have been reported. However, NEMS actuators are still rather far to be employed as force- and torquegenerating elements in biorobotic artifacts.

# Nanotechnology for sensing

Sensing technology is evolving rapidly. Nanosensors are particularly interesting because they would be small, pervasive, and unobtrusive. More importantly, they should be more sensitive than their macroscopic counterparts.

Nanoparticles currently dominate the nanosensors stage, with several applications and working principles. Nanotubes and nanowires were the first to be used as nanoscale sensors, by exploiting their change in conductivity when exposed to specific substances (Kong et al. 2000; Cui et al. 2001). Several nanoparticles are currently known to provide measurable outputs in correspondence to defined input stimuli. Gold nanoparticles are mainly used for the detection of chemical/biological targets, by exploiting a change in either nanoparticle redox properties, surface plasmon resonance properties or conductivity, or by functionalizing their surface with specific recognition elements (e.g., antibodies, oligonucleotides, reactive molecules, etc.) that emit detectable signals, such as fluorescence, when they attach to the desired target (Fig. 2a) (Saha et al. 2012). Electrochemical sensors and biosensors can be also based on other metal nanoparticles (Ag and Pt), oxide nanoparticles $\mathrm { ( S i O _ { 2 } , T i O _ { 2 } , Z r O _ { 2 } }$ , and $\mathbf { M n O } _ { 2 }$ ) and semiconductor nanoparticles (CdS and PbS), by exploiting their capability to act as catalysts or reactants and to enhance electron transfer. In addition, they can be functionalized with specific ligands, as previously described for gold nanoparticles (Luo et al. 2005; Sarkar et al. 2012).

![](img/7ed4b59f6d616bd38e598878b3aad2710141d1d4fbf9cb033117e8100a6bfbab.jpg)  
Fig. 2 Examples of nanoscale sensing strategies based on nanoparticles and nanocomposites. a Physical properties of gold nanoparticles and schematic illustration of the detection principle based on specific ligands attached to the nanoparticle surface. Reproduced with permission from (Saha et al. 2012). Copyright (2012): American Chemical Society. b Matrix of possible CNT-based electrical, mechanical and optical transducers and example of pressure sensor based on a SWCNT. Adapted with permission from (Hierold et al. 2007). Copyright (2007): Elsevier. c Examples of polymeric nanoparticle conformational changes in response to pH shifts or to variations   
in water content. Such mechanisms are usually exploited for triggering drug delivery, but they can be also used to monitor chemical and physical parameters. pBMA stands for poly(nbutyl methacrylate). Adapted from (Motornov et al. 2010). Copyright (2010): Elsevier. d Schematic illustration, scanning electron microscope images and output signal of a piezoelectric nanocomposite (p-NC) based on polydimethylsiloxane (PDMS) and barium titanate $\mathrm { ( B a T i O _ { 3 } ) }$ ) nanoparticles. Adapted with permission from (Park et al. 2012). Copyright (2012): John Wiley and Sons

Carbon nanotubes are probably the most promising nanoparticles for achieving versatile sensing at the nanoscale (Hierold et al. 2007). Thanks to their unique mechanical, electrical, and optical properties, they can be used for the transduction of different physical stimuli, by exploiting distinct detection methods (Fig. 2b). For sensing mechanical units, the electromechanical properties of single-walled CNTs are of most interest. For example, they can be used as nanoscale piezoresistors showing gauge factors up to 1,000 and able to return to their original state, even after a strong deformation, thanks to the high elasticity of the covalent carbon–carbon bonds.

Polymeric nanoparticles have been also recently proposed. Nanoparticles made of polyacrylamide, poly(methyl methacrylate), poly(sodium styrene sulfonate), $N .$ -isopropylacrylamide, and other nondegradable and degradable materials have been mainly used for dynamic monitoring of chemical properties, response to external stimulants, and metabolism of cells and tissues. They are especially useful in biorelated applications, due to their high biocompatibility. To this purpose, they are normally functionalized with target-specific ligands, although their conformational changes in response to specific stimuli

can be also exploited (Fig. 2c) (Motornov et al. 2010;   
Ray and Kopelman 2013).

Although interesting, single nanoparticle-based sensors will be hardly integrated in future biorobotic platforms, due to the difficulties of assembling them in flexible and portable supports and to the common need of detecting their signals with bulky equipment. In fact, as correctly argued by (Requicha 2003), a probe per se is not a usable sensor. Thus, nanoparticles embedded in matrices showing tunable properties (nanocomposites) show more promises, especially concerning their integration potentials. Composites of CNTs and other graphene-related materials have been recently proposed for advanced sensing (Li et al. 2008; Shan et al. 2010; Moktadir 2014).

Nanocomposites based on flexible polymeric matrices and piezoelectric nanoparticles (such as barium titanate, zinc oxide, and alkaline niobate nanoparticles) are nowadays attracting a growing interest. Such devices rely on the transduction of mechanical energy in detectable and storable electrical outputs, by piezoelectric materials (Fig. 2d). They have been mostly proposed as energy harvesting systems (Park et al. 2012; Xu et al. 2013; Jeong et al. 2014). This represents an interesting application in biorobotics, allowing the fabrication of devices able to partly selfrecharge their batteries, when they operate. However, the ability of such systems to respond to both pressure and deformation stimuli make them attractive also as force and stretching/bending sensors with nanoscale features.

Molecules of biological origin have been also proposed as nanosensors: proteins (enzymes, antigens and antibodies) and DNA are the most promising ones (Zhao et al. 2001; Hauser et al. 2014; Dolatabadi and de la Guardia 2014). The sensing mechanism of these molecules is based on a change of conformation when a binding event takes place. The binding may result, for example, in a change of distance between an electrically-active component and an electrode, thus allowing an electrical signal detection. Biological molecules show good promises for a future integration in autonomous or semi-autonomous miniaturized robots, for advanced medical procedures. However, technologies for signal acquisition and processing at a miniaturization level comparable to that of these sensing molecules are not available, at present.

In parallel to the bio-hybrid route for actuation, whole cells have been also proposed as sensing elements (Ricotti et al. 2012; Lucarotti et al. 2013; Adamatzki 2013). In this case, such devices largely exceed the nanoscale dimensions. However, as already mentioned for bio-hybrid actuators, nanoscale materials and fabrication techniques may play a key role in the development of enabling technologies, needed for achieving long-term and usable bio-hybrid sensors to be employed in future robots.

Concerning NEMS, major improvements in fabrication techniques at the nanoscale during the last two decades enabled a good control of nanoscale phenomena and allowed the development of advanced nanosensors by means of focused electron and ion beams, nanoimprint, nanosphere lithographies, direct laser writing and other techniques (Guillot and de la Chapelle 2012). These efforts permitted, for example, to fabricate sub-micrometric cantilevers showing excellent sensitivities (Li et al. 2007) and silicon nanopillars for mass sensing with femtogram resolution (Wasisto et al. 2014). Although several steps will be needed for efficiently integrate them in robotic structures, NEMS sensors show promises for future biorobotic applications.

# Robots, microrobots, and nanorobots: challenges and perspectives

The field of microrobotics significantly advanced in the last decade and is currently populated by several research prototypes (Nelson et al. 2010; Khalil et al. 2014; Lucarini et al. 2014) and few attempts to reach the industrial stage, in a relatively short time-frame (Kummer et al. 2010). Nanotechnology applications are expected to play a key role for the fabrication of microrobot components, but the current size of these systems is still rather large $( > 1 0 0 \ \mu \mathrm { m } )$ .

The development of true nanorobots is an exciting challenge, but this field is currently mainly characterized by research efforts focused on design roadmaps and simulations (Lenaghan et al. 2013; Hamdi and Ferreira 2014). The most recognized obstacles for the development of functional nanorobots are (i) the lack of suitable actuation technologies at the nanoscale; (ii) the lack of miniaturized electronics, needed to acquire and elaborate signals from nanosensors; (iii) nanoassembly and nanomanipulation issues.

Artificial molecular machines, biomotors, nanoparticle-based motors, and NEMS, despite significant advancements in the last decade, do not seem to represent viable solutions for the development of integrable actuators in nanorobots, in the short/medium-term. Bio-hybrid actuation based on bacteria and whole muscle cells is promising and it could theoretically revolutionize the field of microrobotics if proper enabling technologies will be developed in the near future. However, muscle cells cannot be scaled in size below few micro-meters. Although the dimensions of a single cell may fit those of the microvasculature, the assembly of several cells to form multinucleated contractile myotubes or simply to provide several degrees of freedom to the microrobot, may be difficult. Thus, sub-micrometric bioactuators would show even greater potential. Bacteria flagella fit these requirements, being less than $2 0 0 ~ \mathrm { { n m } }$ in size (if the cell body is neglected). However, the risks of pathogenicity due to bacteria-derived materials and a scarce controllability limit their clinical use, at present.

Additional strategies are emerging, such as catalytic reactions to achieve nanometer locomotion in chemically well-defined environments (Bao et al. 2014; Fomin et al. 2014). At present, one of the most promising strategies for actuation at the nanoscale is represented by the action of remotely controlled fields. This is almost the only viable actuation solution in the case of relatively high Reynold’s hydrodynamic conditions (arteries and arterioles): magnetic nanoparticles or nanoparticle aggregates can be remotely steered by magnetic fields (thus allowing locomotion), while additional degrees of freedom can be triggered by either alternate magnetic fields (e.g., inducing hyperthermia or other phenomena), ultrasound (e.g., by means of pressure gradients), optical stimuli or other ‘‘wireless’’ solutions. In smaller districts (microvasculature), characterized by low Reynold’s number regimes, magnetic locomotion is still a valuable option, but other strategies may find room in the near future if technological limitations will be overcome. These include bacterial locomotion (including approaches based on artificial flagella) and microjet catalytic reactions (if this technology will be further scaled down without losing autonomy and if the fuel will be made bio- and hemo-compatible).

A relatively wide range of nanosensors could be theoretically integrated in nanorobots, since different sensing technologies have reached nowadays a good maturation level. Nanocomposites (especially those based on graphene-related materials), antibodies and other biomolecules show the best promises. Nanoelectromechanical system sensors may be also suitable for being exploited in integrated nanorobots. However, due to the currently unsolved difficulties in acquiring and processing electrical signals at the nanoscale, it would be desirable that such sensors would be directly able to convert a change in the surrounding environment to a desired nanorobot action. Indeed, this concept of sensing is what marks the difference between a nanosystem able to simply send a feedback to an operator (that then will act on the systems, e.g., by tuning magnetic fields or other triggering sources) and a nanosystem truly intelligent and partly autonomous. To this purpose, the most promising class of nanosensors is represented by molecules of biological origin, whose working principle is grounded on conformational changes. The assembly of several biological molecules in proper ‘‘sensing chains’’ would allow to build pathways of response within the nanorobot structure, thus partly mimicking the incredibly complex net of intracellular biochemical pathways that regulate the behavior of living cells. Biotechnological efforts could support this vision, by engineering such molecules, thus enhancing their sensing or triggering capabilities. This is still rather far to be achieved, but recent discoveries opened new routes for preparing nonnatural sequence-controlled molecules (Lutz et al. 2013).

Nanoassembly and nanomanipulation remain important challenges, but encouraging results have been recently achieved. Advanced nanomanipulation procedures have been developed by means of atomic force microscope (AFM) (Hou et al. 2013), electron beam (Verbeeck et al. 2013), magnetic trapping (Grange and Strick 2013), and laser trapping (Argawal et al. 2005). In parallel, advanced self-assembly strategies have been proposed (Bishop et al. 2009; Rajendran et al. 2012).

Not only micro- and nanorobots, but also larger robotic devices will surely benefit from the opportunities provided by the advancement of nanotechnology, described in the previous sections. For example, a recent avenue of biorobotics targets the development of compliant soft robots, able to perform advanced tasks impossible to be achieved by traditional rigid ones (Albu-Schaffer et al. 2008; Laschi et al. 2012; Kim et al. 2013). This novel paradigm obviously needs ad hoc strategies enabling the actuation and sensorization of flexible and deformable structures. Nanoparticles and nanocomposites associated with smart materials may represent suitable actuation technologies, as well as bio-hybrid muscle cell-based systems. On the other hand, flexible polymeric matrices with embedded piezoelectric or other types of responsive nanoparticles may represent suitable sensorization solutions for soft structures, being compliant with robot deformation and being able, at the same time, to provide detectable signals in response to pressure and bending/stretching stimuli.

# Conclusion

Biorobotics is an interdisciplinary and evolving research field, which targets the development of advanced machines and the comprehension of living beings’ behavior. Nanotechnology applications open a series of opportunities for the development of nonconventional robots, microrobots, and nanorobots. Nanorobots, in particular, represent a high-risk/highreward challenge, which requires the development and integration of suitable actuation and sensing technologies. At present, the most promising strategy for nanorobot actuation is based on the action of remote force fields, mediated by responsive nanoparticles or other materials at the nanoscale. A number of nanosensor technologies may be applied to nanorobots, but the current lack of miniaturized electronic systems directs the choice toward molecules of biological origins, whose sensing mechanism is based on conformational changes after binding events. Besides driving the design and development of functional micro- and nanorobot components, nanotechnology will also contribute to a re-conceptualization of larger robotic devices.

# References

Adamatzki A (2013) Slime mould tactile sensor. Sens Actuators B 188:38–44   
Albu-Schaffer A, Eiberger O, Grebenstein M, Haddadin S, Ott C, Wimbock T, Wolf S, Hirzinger G (2008) Soft robotics. IEEE Robot Autom Mag 15(3):20–30   
Argawal R, Ladavac K, Roichman Y, Yu G, Lieber CM, Grier DG (2005) Manipulation and assembly of nanowires with holographic optical traps. Opt Express 13(22):8906–8912   
Badjic´ JD, Balzani V, Credi A, Silvi S, Stoddart JF (2004) A molecular elevator. Science 303(5665):1845–1849   
Balzani V, Credi A, Raymo FM, Stoddart JF (2000) Artificial molecular machines. Angew Chem Int Ed 39:3348–3391   
Bao J, Yang Z, Nakajima N, Shen Y, Takeuchi M, Huang Q, Fukuda T (2014) Self-actuating asymmetric platinum catalytic mobile nanorobot. IEEE Trans Robot 30(1):33–39   
Bath J, Turberfield AJ (2007) DNA nanomachines. Nat Nanotechnol 2(5):275–284   
Baughman RH, Cui C, Zakhidov AA, Iqbal Z, Barisci JN, Spinks GM, Wallace GG, Mazzoldi A, De Rossi D, Rinzler AG, Jaschinski O, Roth S, Kertesz M (1999) Carbon nanotube actuators. Science 284(5418):1340–1344   
Berne RW (2004) Towards the conscientious development of ethical nanotechnology. Sci Eng Ethics 10(4):627–638   
Bishop KJ, Wilmer CE, Soh S, Grzybowski BA (2009) Nanoscale forces and their uses in self-assembly. Small 5(14):1600–1630   
Cai H, Xu KJ, Liu AQ, Fang Q, Yu MB, Lo GQ, Kwong DL (2012) Nano-opto-mechanical actuator driven by gradient optical force. Appl Phys Lett 100(1):013108   
Chan V, Asada HH, Bashir R (2014) Utilization and control of bioactuators across multiple length scales. Lab Chip 14:653–670   
Coskun A, Banaszak M, Astumian RD, Stoddart JF, Grzybowski BA (2012) Great expectations: can artificial molecular machines deliver on their promise? Chem Soc Rev 41:19–30   
Cui Y, Wei Q, Park H, Lieber CM (2001) Nanowire sensors for highly sensitive and selective detection of biological and chemical species. Science 293:1289–1292   
Dario P (2005) Biorobotics. J Robot Soc Jpn 23(5):552–554   
Dario P, Guglielmelli E, Allotta B, Carrozza MC (1996) Robotics for medical applications. IEEE Robot Autom Mag 3(3):44–56   
Dario P, Hannaford B, Takanishi A (2008) Guest editorial special issue on biorobotics. IEEE Trans Robot 24(1):3–4   
Dennis JR, Howard J, Vogel V (1999) Molecular shuttles: directed motion of microtubules along nanoscale kinesin tracks. Nanotechnology 10(3):232–236   
Dolatabadi JEN, de la Guardia M (2014) Nanomaterial-based electrochemical immunosensors as advanced diagnostic tools. Anal Methods 6:3891–3900   
Dong L, Nelson BJ (2007) Tutorial-robotics in the small part II: nanorobotics. IEEE Robot Autom Mag 14(3):111–121   
Ekinci KL (2005) Electromechanical transducers at the nanoscale: actuation and sensing of motion in nanoelectromechanical systems (NEMS). Small 1(8–9):786–797   
Fennimore AM, Yuzvinsky TD, Han WQ, Fuhrer MS, Cumings J, Zetti A (2003) Rotational actuators based on carbon nanotubes. Nature 424(6947):408–410   
Ferreira A, Martel S (2014) Guest editorial: special issue on nanorobotics. IEEE Trans Robot 30(1):1–2   
Fomin VM, Hippler M, Magdanz V, Soler L, Sanchez S, Schmidt OG (2014) Propulsion mechanism of catalytic microjet engines. IEEE Trans Robot 30(1):40–48   
Fusco S, Sakar MS, Kennedy S, Peters C, Bottani R, Starsich F, Mao A, Sotiriou GA, Pane´ S, Pratsinis SE, Mooney D, Nelson BJ (2013) An integrated microrobotic platform for on-demand, targeted therapeutic interventions. Adv Mater   
Grange W, Strick TR (2013) Magnetic trapping of single molecules: principles, developments, and applications. Proc Int Soc Opt Eng (SPIE) 8810:88101H   
Grunwald A (2005) Nanotechnology—A new field of ethical inquiry? Sci Eng Ethics 11(2):187–201   
Guillot N, de la Chapelle ML (2012) Lithographied nanostructures as nanosensors. J Nanophoton 6(1):064506   
Hamdi M, Ferreira A (2014) Guidelines for the design of magnetic nanorobots to cross the blood-brain barrier. IEEE Trans Robot 30(1):81–92   
Hauser CAE, Maurer-Stroh S, Martins IC (2014) Amyloidbased nanosensors and nanodevices. Chem Soc Rev 43(15):5326–5345   
Hergt R, Dutz S, Ro¨der M (2008) Effects of size distribution on hysteresis losses of magnetic nanoparticles for hyperthermia. J Phys 20(38):385214   
Hierold C, Jungen A, Stampfer C, Helbling T (2007) Nano electromechanical sensors based on carbon nanotubes. Sens Actuators A 136:51–61   
Hou J, Liu L, Wang Z, Wang Z, Xi N, Wang Y, Wu C, Dong Z, Yuan S (2013) AFM-based robotic nano-hand for stable manipulation at nanoscale. IEEE Trans Autom Sci Eng 10(2):285–295   
Jeong CK, Park KI, Ryu J, Hwang GT, Lee KJ (2014) Largearea and flexible lead-free nanocomposite generator using alkaline niobate particles and metal nanorod filler. Adv Funct Mater 24:2620–2629   
Kamm RD, Bashir R (2014) Creating living cellular machines. Ann Biomed Eng 42(2):445–459   
Khalil ISM, Dijkslag HC, Abelmann L, Misra S (2014) MagnetoSperm: a microrobot that navigates using weak magnetic fields. Appl Phys Lett 104(22):223701   
Kim S, Laschi C, Trimmer B (2013) Soft robotics: a bioinspired evolution in robotics. Trends Biotechnol 31(5):287–294   
Kong J, Franklin NR, Zhou C, Chapline MC, Peng S, Cho K, Dai H (2000) Nanotube molecular wires as chemical sensors. Science 2887(5463):622–625   
Kummer MP, Abbott JJ, Kratochvil BE, Borer R, Sengul A, Nelson BJ (2010) Octomag: an electromagnetic system for 5-DOF wireless micromanipulation. IEEE Trans Robot 26(6):1006–1017   
Laschi C, Cianchetti M, Mazzolai B, Margheri L, Follador M, Dario P (2012) Soft robot arm inspired by the octopus. Adv Robot 26(7):709–727   
Lenaghan SC, Wang Y, Xi N, Fukuda T, Tarn T, Hamel WR, Zhang M (2013) Grand challenges in bioengineered nanorobotics for cancer therapy. IEEE Trans Biomed Eng 60(3):667–673   
Li M, Tang HX, Roukes ML (2007) Ultra-sensitive NEMSbased cantilevers for sensing, scanned probe and very highfrequency applications. Nat Nanotechnol 2:114–120   
Li C, Thostenson ET, Chou TW (2008) Sensors and actuators based on carbon nanotubes and their composites: a review. Compos Sci Technol 68:1227–1249   
Liedl T, Sobey TL, Simmel FC (2007) DNA-based nanodevices. Nano Today 2(2):36–41   
Lucarini G, Palagi S, Beccai L, Menciassi A (2014) A powerefficient propulsion method for magnetic microrobots. Int J Adv Robot Syst 11(1):116   
Lucarotti C, Oddo CM, Vitiello N, Carrozza MC (2013) Synthetic and bio-artificial tactile sensing: a review. Sensors 13(2):1435–1466   
Luo X, Morrin A, Killard AJ, Smyth MR (2005) Application of nanoparticles in electrochemical sensors and biosensors. Electroanalysis 18(4):319–326   
Lutz JF, Ouchi M, Liu DR, Sawamoto M (2013) Sequencecontrolled polymers. Science 341(6146):1238149   
Magdanz V, Sanchez S, Schmidt OG (2013) Development of a sperm-flagella driven micro-bio-robot. Adv Mater 25: 6581–6588   
Martel S, Tremblay CC, Ngakeng S, Langlois G (2006) Controlled manipulation and actuation of micro-objects with magnetotactic bacteria. Appl Phys Lett 89:233904   
Martel S, Felfoul O, Mathieu JB, Chanu A, Tamaz S, Mohammadi M, Mankiewicz M, Tabatabaei N (2009a) MRI-based medical nanorobotic platform for the control of magnetic nanoparticles and flagellated bacteria for target interventions in human capillaries. Int J Robot Res 28(9):1169– 1182   
Martel S, Mohammadi M, Felfoul O, Lu Z, Pouponneau P (2009b) Flagellated magnetotactic bacteria as controlled MRI-trackable propulsion and steering systems for medical nanorobots operating in the human microvasculature. Int J Robot Res 28(4):571–582   
Moktadir Z (2014) Graphene nanoelectromechanics (NEMS). Graphene: Properties, preparation, characterisation and devices. Southampton University, UK, p 341. doi: 10.1533/ 9780857099334.3.341   
Montemagno C, Bachand G (1999) Constructing nanomechanical devices powered by molecular motors. Nanotechnology 10(3):225–231   
Motornov M, Roiter Y, Tokarev I, Minko S (2010) Stimuliresponsive nanoparticles, nanogels and capsules for integrated multifunctional intelligent systems. Prog Polym Sci 35:174–211   
Nelson BJ, Kaliakatsos IK, Abbott JJ (2010) Microrobots for minimally invasive medicine. Ann Rev Biomed Eng 12:55–85   
Park KI, Lee M, Liu Y, Moon S, Hwang GT, Zhu G, Kim JE, Kim SO, Kim DK, Wang ZL, Lee KJ (2012) Flexible nanocomposite generator made of $\mathrm { B a T i O } _ { 3 }$ nanoparticles and graphitic carbons. Adv Mater 24:2999–3004   
Park SJ, Park SH, Cho S, Kim DM, Lee Y, Ko SY, Hong Y, Choy HE, Min JJ, Park JO, Park S (2013) New paradigm for tumor theranostic methodology using bacteria-based microrobot. Sci Rep 3:3394   
Park SJ, Lee Y, Choi YJ, Cho S, Jung HE, Zheng S, Park BJ, Ko SY, Park JO, Park S (2014) Monocyte-based microrobot with chemotactic motility for tumor theragnosis. Biotechnol Bioeng 111(10):2132–2138   
Raguse B, Mu¨ller KH, Wieczorek L (2003) Nanoparticle actuators. Adv Mater 15(11):922–926   
Rajendran A, Endo M, Sugiyama H (2012) DNA Origami: synthesis and self-assembly. Curr Prot Nucl Acid Chem. doi: 10.1002/0471142700.nc1209s48   
Ray A, Kopelman R (2013) Hydrogel nanosensors for biophotonic imaging of chemical analytes. Nanomedicine 8(11):1829–1838   
Requicha AAG (2003) Nanorobots, NEMS, and nanoassembly. Proc IEEE 91(11):1922–1933   
Ricotti L, Menciassi A (2012) Bio-hybrid muscle cell-based actuators. Biomed Microdev 14(6):987–998   
Ricotti L, Menciassi A, Morishima K (2012) Guest editorial introduction to the special issue on bio-hybrid systems and living machines. Biomed Microdev 14(6):965–967   
Ricotti L, Fujie T, Vaza˜o H, Ciofani G, Marotta R, Brescia R, Filippeschi C, Corradini I, Matteoli M, Mattoli V, Ferreira L, Menciassi A (2013) Boron nitride nanotube-mediated stimulation of cell co-culture on micro-engineered hydrogels. PLoS One 8(8):e71707   
Ricotti L, das Neves RP, Ciofani G, Canale C, Nitti S, Mattoli V, Mazzolai B, Ferreira L, Menciassi A (2014) Boron nitride nanotube-mediated stimulation modulates F/G-actin ratio and mechanical properties of human dermal fibroblasts. J Nanop Res 16(2):1–14   
Saha K, Agasti SS, Kim C, Li X, Rotello VM (2012) Gold nanoparticles in chemical and biological sensing. Chem Rev 112:2739–2779   
Sakar MS, Neal D, Boudou T, Borochin MA, Li Y, Weiss R, Kamm RD, Chen CS, Asada HH (2012) Formation and optogenetic control of engineered 3D skeletal muscle bioactuators. Lab Chip 12:4976–4985   
Sarkar S, Guibal E, Quignard F, Sengupta AK (2012) Polymersupported metals and metal oxide nanoparticles: synthesis, characterization, and applications. J Nanop Res 14(2):1–24   
Shan C, Yang H, Han D, Zhang Q, Ivaska A, Niu L (2010) Graphene/AuNPs/chitosan nanocomposites film for glucose biosensing. Biosens Bioelectron 25(5):1070–1074   
Sitti M (2009) Miniature devices: voyage of the microrobots. Nature 458:1121–1122   
Tasoglu S, Diller E, Guven S, Sitti M, Demirci U (2014) Untethered micro-robotic coding of three-dimensional material composition. Nat Commun. doi: 10.1038/ncomms4124   
Terasawa N, Hayashi Y, Koga T, Higashi N, Asaka K (2014) High-performance polymer actuators based on poly (ethylene oxide) and single-walled carbon nanotube–ionic liquid-based gels. Sens Actuators B Chem 202:382–387   
Tiang F, Zhou G, Du Y, Chau FS (2013) Applications of nanoelectromechanical actuators in nano optomechanics. Opt MEMS Nanophoton (OMN) 173–174. doi:10.1109/OMN. 2013.6659115   
Verbeeck J, Tian H, Van Tendeloo G (2013) How to manipulate nanoparticles with an electron beam? Adv Mater 25(8): 1114–1117   
Veruggio G, Operto F (2008) Roboethics: social and ethical implications of robotics. In: Siciliano B, Khatib O (eds) Springer handbook of robotics. Springer, Berlin, pp 1499–1524   
Wahajuddin, Arora S (2012) Superparamagnetic iron oxide nanoparticles: magnetic nanoplatforms as drug carriers. Int J Nanomedicine 7:3445–3471   
Wasisto HS, Huang K, Merzsch S, Stranz A, Waag A, Peiner E (2014) Finite element modeling and experimental proof of NEMS-based silicon pillar resonators for nanoparticle mass sensing applications. Microsyst Technol 20(4–5):571–584   
Xi J, Schmidt JJ, Montemagno CD (2005) Self-assembled microdevices driven by muscle. Nat Mater 4:180–184   
Xu S, Yeh Y, Poirier G, McAlpine MC, Register RA, Yao N (2013) Flexible piezoelectric PMN-PT nanowire-based nanocomposite and device. Nano Lett 13:2393–2398   
Ye Z, Sitti M (2014) Dynamic trapping and two-dimensional transport of swimming microorganisms using a rotating magnetic microrobot. Lab Chip 14:2177–2182   
Zhao HQ, Lin L, Li JR, Tang JA, Duan MX, Jiang L (2001) DNA biosensor with high sensitivity amplified by gold nanoparticles. J Nanop Res 3(4):321–323   
Zhou L, Marras AE, Su HJ, Castro CE (2013) DNA origami compliant nanostructures with tunable mechanical properties. ACS Nano 8(1):27–34