import os
import re
import copy
import json
import gc
import time
import traceback
import tempfile
import shutil
from pathlib import Path
import logging
import torch
import pypdfium2

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Add Mineru to Python path (UPDATE THIS PATH)
import sys
sys.path.append("C:/Path/To/Your/Mineru")  # UPDATE THIS

# Mineru imports
try:
    from mineru.cli.common import convert_pdf_bytes_to_bytes_by_pypdfium2
    from mineru.data.data_reader_writer import FileBasedDataWriter
    from mineru.utils.enum_class import MakeMode
    from mineru.backend.pipeline.pipeline_analyze import doc_analyze as pipeline_doc_analyze
    from mineru.backend.pipeline.pipeline_middle_json_mkcontent import union_make as pipeline_union_make
    from mineru.backend.pipeline.model_json_to_middle_json import result_to_middle_json as pipeline_result_to_middle_json
    logger.info("Mineru imports successful")
except ImportError as e:
    logger.error(f"Mineru import failed: {str(e)}")
    raise

def cleanup_resources():
    """Force cleanup of resources between batches"""
    gc.collect()
    time.sleep(0.5)
    if 'pypdfium2' in globals():
        pypdfium2.PdfDocument.__del__ = lambda self: None

def sanitize_filename(name, max_length=40):
    """Strict filename sanitization"""
    name = re.sub(r'[<>:"/\\|?*\x00-\x1f]', '', name)
    name = re.sub(r'[\s.]+', '_', name)
    return name[:max_length].strip('_.')

def safe_prepare_env(output_dir, pdf_file_name):
    """Create output directories"""
    try:
        safe_name = sanitize_filename(pdf_file_name)
        base_dir = Path(output_dir) / safe_name[:30]
        
        image_dir = base_dir / "img"
        md_dir = base_dir / "out"
        
        for d in [base_dir, image_dir, md_dir]:
            d.mkdir(parents=True, exist_ok=True)
        
        return str(image_dir), str(md_dir)
    except Exception as e:
        logger.error(f"Directory creation failed: {str(e)}")
        raise

def process_pdf_with_mineru(pdf_path, output_dir):
    """Process a PDF file with Mineru and return extracted text"""
    try:
        # 1. Read file
        with open(pdf_path, 'rb') as f:
            pdf_bytes = f.read()
        
        # 2. Prepare environment
        pdf_name = Path(pdf_path).stem
        image_dir, md_dir = safe_prepare_env(output_dir, pdf_name)
        
        # 3. Convert pages
        try:
            pdf_bytes = convert_pdf_bytes_to_bytes_by_pypdfium2(pdf_bytes, 0, None)
        except Exception as e:
            logger.warning(f"Page conversion failed, using original: {str(e)}")
        
        # 4. Process with Mineru
        infer_results, all_image_lists, all_pdf_docs, lang_list, ocr_enabled_list = pipeline_doc_analyze(
            [pdf_bytes], ["en"], parse_method="auto", formula_enable=True, table_enable=False
        )
        
        # 5. Generate outputs
        model_json = copy.deepcopy(infer_results[0])
        image_writer = FileBasedDataWriter(image_dir)
        md_writer = FileBasedDataWriter(md_dir)
        
        middle_json = pipeline_result_to_middle_json(
            infer_results[0], all_image_lists[0], all_pdf_docs[0],
            image_writer, "en", ocr_enabled_list[0], True
        )
        
        # 6. Save outputs
        pdf_info = middle_json["pdf_info"]
        
        # Generate markdown content
        md_content_str = pipeline_union_make(pdf_info, MakeMode.MM_MD, os.path.basename(image_dir))
        md_file_path = os.path.join(md_dir, f"{pdf_name}.md")
        with open(md_file_path, 'w', encoding='utf-8') as f:
            f.write(md_content_str)
        
        # 7. Read and clean markdown content
        with open(md_file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Clean markdown content
        cleaned_content = clean_md_content(content)
        
        logger.info(f"Successfully processed: {pdf_name}")
        return cleaned_content
        
    except Exception as e:
        logger.error(f"PDF processing failed: {str(e)}")
        traceback.print_exc()
        raise
    finally:
        cleanup_resources()

def clean_md_content(content):
    """Clean up markdown content for better processing"""
    # Remove images and special tags
    content = re.sub(r'!\[.*?\]\(.*?\)', '', content)
    content = re.sub(r'\{.*?\}', '', content)
    
    # Remove excessive newlines
    content = re.sub(r'\n{3,}', '\n\n', content)
    
    return content.strip()