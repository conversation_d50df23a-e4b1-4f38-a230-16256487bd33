import logging
from typing import List, AsyncGenerator
import aiohttp
import json

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


"""
class LocalLLMClient:
    def __init__(self):
        self.ollama_url = "http://localhost:11434"  # Ollama default URL
        self.model_name = "llama3:8b-instruct"      # Quantized 8B model

    async def generate_text(self, prompt: str, max_tokens: int = 500) -> str:
        # Generate text using Ollama's API
        try:
            async with aiohttp.ClientSession() as session:
                payload = {
                    "model": self.model_name,
                    "prompt": prompt,
                    "stream": False,
                    "options": {"temperature": 0.7, "max_tokens": max_tokens}
                }
                async with session.post(
                    f"{self.ollama_url}/api/generate",
                    json=payload
                ) as response:
                    data = await response.json()
                    return data.get("response", "")
        except Exception as e:
            logger.error(f"Generation failed: {str(e)}")
            return "Error: LLM unavailable"

    async def generate_points(self, text: str) -> List[str]:
        prompt = f"Extract 3-5 concise research points from this text:
        {text[:2000]}
        Format as bullet points starting with '-'"
        response = await self.generate_text(prompt)
        return [line.strip() for line in response.split('\n') if line.strip()]

    async def generate_from_prompt(self, prompt: str, context: str) -> List[str]:
        prompt = f"Based on this context:
        {context[:1500]}
        Generate 3 specific points about: {prompt}
        Format each point with a '-' prefix"
        response = await self.generate_text(prompt)
        return [line.strip() for line in response.split('\n') if line.strip()]

    async def rag_discussion(self, prompt: str, documents: List[str]) -> str:
        context = "\n\n".join([doc[:1000] for doc in documents])
        prompt = f"Discuss this topic: {prompt}
        Using these research excerpts:
        {context}
        Provide a detailed response with citations like [1], [2]"
        return await self.generate_text(prompt, max_tokens=800)
    # llm_client.py - Add these new functions
    async def generate_structured_points(self, prompt: str, papers: List[dict]) -> List[str]:
        #Generate well-formatted research points with proper citations
        context = "\n\n".join(
            f"Paper {i+1} ({p['filename']}):\n" + 
            "\n".join(p['points'][:3]) + 
            f"\nContent excerpt: {p['content'][:1000]}..."
            for i, p in enumerate(papers)
        )

        prompt = f"Generate 3-5 well-structured research points about: {prompt}
        Using these relevant papers as context:
        {context}
        
        Format each point EXACTLY like this:
        • [Concise point] (Source: [Shortened filename])
        
        Ensure points are:
        - Clear and readable
        - Directly relevant to the prompt
        - Properly cited
        - Cover different aspects of the topic"
        
        response = await self.generate_text(prompt, max_tokens=800)
        return [line.strip() for line in response.split('\n') if line.strip().startswith('•')]

    async def generate_discussion(self, prompt: str, papers: List[dict]) -> str:
        #Generate a detailed discussion using RAG
        context = "\n\n".join(
            f"Paper {i+1} ({p['filename']}):\n" + 
            "\n".join(p['points']) + 
            f"\nContent excerpt: {p['content'][:1000]}..."
            for i, p in enumerate(papers))
        
        prompt = f"Write a detailed discussion about: {prompt}
        Using these research papers as sources:
        {context}
        
        Structure your response:
        1. Overview of key findings
        2. Analysis of different perspectives
        3. Critical evaluation
        4. Potential implications
        
        Cite sources like [1], [2] etc. where appropriate."
        
        return await self.generate_text(prompt, max_tokens=1200)
"""