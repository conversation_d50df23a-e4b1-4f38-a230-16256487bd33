# database.py
import sqlite3
import json
from config import settings
from fastapi import HTTPException
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# In your init_db() function:
def init_db():
    try:
        conn = sqlite3.connect(settings.database_path)
        c = conn.cursor()
        c.execute('''
            CREATE TABLE IF NOT EXISTS papers (
                id TEXT PRIMARY KEY,
                filename TEXT,
                content TEXT,
                points TEXT,
                content_length INTEGER,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        conn.commit()
        conn.close()
        logger.info("Database initialized with new schema")
    except Exception as e:
        logger.error(f"Database init failed: {str(e)}")
        raise

def store_paper(paper_id, filename, content, points):
    try:
        conn = sqlite3.connect(settings.database_path)
        c = conn.cursor()
        
        # Get file size from content
        content_size = len(content) if content else 0
        
        c.execute(
            """INSERT INTO papers (id, filename, content, points, content_length) 
            VALUES (?, ?, ?, ?, ?)""",
            (paper_id, filename, content, 
             json.dumps(points) if not isinstance(points, str) else points,
             content_size)
        )
        conn.commit()
        logger.info(f"Stored paper: {filename} (Size: {content_size} bytes)")
    except Exception as e:
        logger.error(f"Failed to store paper: {str(e)}")
        raise
    finally:
        if conn:
            conn.close()

def get_relevant_papers(prompt, top_n=3):
    try:
        conn = sqlite3.connect(settings.database_path)
        conn.row_factory = sqlite3.Row
        c = conn.cursor()
        
        c.execute("SELECT id, filename, content, points FROM papers")
        papers = c.fetchall()
        
        if not papers:
            return []

        # Convert to dict and handle points parsing
        processed_papers = []
        for paper in papers:
            try:
                points = json.loads(paper['points']) if paper['points'] else []
            except json.JSONDecodeError:
                points = [paper['points']] if paper['points'] else []
                
            processed_papers.append({
                'id': paper['id'],
                'filename': paper['filename'],
                'content': paper['content'],
                'points': points
            })
        
        return processed_papers
        
    except Exception as e:
        logger.error(f"🚨 Paper retrieval failed: {str(e)}")
        raise HTTPException(500, "Paper retrieval error")
    finally:
        conn.close()
        
# database.py - Add this new function
async def get_relevant_papers_by_points(prompt: str):
    try:
        conn = sqlite3.connect(settings.database_path)
        conn.row_factory = sqlite3.Row
        c = conn.cursor()
        
        # Get all papers with their points
        c.execute("SELECT id, filename, content, points FROM papers")
        papers = c.fetchall()
        
        if not papers:
            return []

        # Process papers and calculate relevance
        processed_papers = []
        for paper in papers:
            try:
                points = json.loads(paper['points']) if paper['points'] else []
            except json.JSONDecodeError:
                points = [paper['points']] if paper['points'] else []
                
            # Calculate relevance score based on prompt and points
            relevance_score = sum(
                1 for point in points 
                if isinstance(point, str) and prompt.lower() in point.lower()
            )
            
            processed_papers.append({
                'id': paper['id'],
                'filename': paper['filename'],
                'content': paper['content'],
                'points': points,
                'relevance': relevance_score
            })
        
        # Sort by relevance and return top N
        return sorted(
            processed_papers,
            key=lambda x: x['relevance'],
            reverse=True
        )[:len(processed_papers)-1]

    except Exception as e:
        logger.error(f"Relevant papers retrieval failed: {str(e)}")
        raise HTTPException(500, "Paper retrieval error")
    finally:
        conn.close()