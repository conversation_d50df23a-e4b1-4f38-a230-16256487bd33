<html><head>
  <link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.15.4/css/all.css">
<style>
  .link-block {
    border: 1px solid transparent;
    border-radius: 24px;
    background-color: rgba(54, 54, 54, 1);
    cursor: pointer !important;
  }
  .link-block:hover {
    background-color: rgba(54, 54, 54, 0.75) !important;
    cursor: pointer !important;
  }
  .external-link {
    display: inline-flex;
    align-items: center;
    height: 36px;
    line-height: 36px;
    padding: 0 16px;
    cursor: pointer !important;
  }
  .external-link,
  .external-link:hover {
    cursor: pointer !important;
  }
  a {
    text-decoration: none;
  }
</style></head>

<body>
  <div style="
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      text-align: center;
      background: linear-gradient(45deg, #007bff 0%, #0056b3 100%);
      padding: 24px;
      gap: 24px;
      border-radius: 8px;
    ">
    <div style="
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 16px;
      ">
      <div style="display: flex; flex-direction: column; gap: 8px">
        <h1 style="
            font-size: 48px;
            color: #fafafa;
            margin: 0;
            font-family: 'Trebuchet MS', 'Lucida Sans Unicode',
              'Lucida Grande', 'Lucida Sans', Arial, sans-serif;
          ">
          MinerU 2: PDF Extraction Demo
        </h1>
      </div>
    </div>

    <p style="
        margin: 0;
        line-height: 1.6rem;
        font-size: 16px;
        color: #fafafa;
        opacity: 0.8;
      ">
      A one-stop, open-source, high-quality data extraction tool that supports converting PDF to Markdown and JSON.<br>
    </p>
    <style>
      .link-block {
        display: inline-block;
      }
      .link-block + .link-block {
        margin-left: 20px;
      }
    </style>

    <div class="column has-text-centered">
      <div class="publication-links">
        <!-- Code Link. -->
        <span class="link-block">
          <a href="https://github.com/opendatalab/MinerU" class="external-link button is-normal is-rounded is-dark" style="text-decoration: none; cursor: pointer">
            <span class="icon" style="margin-right: 4px">
              <i class="fab fa-github" style="color: white; margin-right: 4px"></i>
            </span>
            <span style="color: white">Code</span>
          </a>
        </span>

        <!-- arXiv Link. -->
        <span class="link-block">
          <a href="https://arxiv.org/abs/2409.18839" class="external-link button is-normal is-rounded is-dark" style="text-decoration: none; cursor: pointer">
            <span class="icon" style="margin-right: 8px">
              <i class="fas fa-file" style="color: white"></i>
            </span>
            <span style="color: white">Paper</span>
          </a>
        </span>

        <!-- Homepage Link. -->
        <span class="link-block">
          <a href="https://mineru.net/home?source=online" class="external-link button is-normal is-rounded is-dark" style="text-decoration: none; cursor: pointer">
            <span class="icon" style="margin-right: 8px">
              <i class="fas fa-home" style="color: white"></i>
            </span>
            <span style="color: white">Homepage</span>
          </a>
        </span>

        <!-- Client Link. -->
        <span class="link-block">
          <a href="https://mineru.net/client?source=online" class="external-link button is-normal is-rounded is-dark" style="text-decoration: none; cursor: pointer">
            <span class="icon" style="margin-right: 8px">
              <i class="fas fa-download" style="color: white"></i>
            </span>
            <span style="color: white">Download</span>
          </a>
        </span>

      </div>
    </div>

    <!-- New Demo Links -->
  </div>


</body></html>