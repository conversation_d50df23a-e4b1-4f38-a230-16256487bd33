import os
import logging
from pydantic_settings import BaseSettings

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class Settings(BaseSettings):
    database_path: str = "/workspaces/bytewise-module-prototyping-2/research.db"
    mineru_output_dir: str = "/workspaces/bytewise-module-prototyping-2/mineru_output"
    upload_dir: str = "/workspaces/bytewise-module-prototyping-2/uploads"
    
    class Config:
        env_file = ".env"

settings = Settings()

# Create directories if they don't exist
try:
    os.makedirs(settings.mineru_output_dir, exist_ok=True)
    os.makedirs(settings.upload_dir, exist_ok=True)
    logger.info(f"Created directories: {settings.mineru_output_dir}, {settings.upload_dir}")
except Exception as e:
    logger.error(f"Directory creation failed: {str(e)}")