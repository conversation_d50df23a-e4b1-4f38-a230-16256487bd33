import os
import uuid
import tempfile
from fastapi import FastAPI, File, UploadFile, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import FileResponse, JSONResponse
from mineru_processor import process_pdf_with_mineru
from database import store_paper, get_relevant_papers_by_points, init_db
from schemas import GenerateRequest
from config import settings
import logging
import sqlite3
import json
import aiohttp
from openai import AsyncOpenAI
import re
# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(title="Bytewise Research Assistant")

# CORS configuration
app.add_middleware(
        CORSMiddleware,
            allow_origins=["*"],
                allow_credentials=True,
                    allow_methods=["*"],
                        allow_headers=["*"],
                        )


@app.get("/database")
async def view_database():
    try:
        conn = None
        try:
            conn = sqlite3.connect(settings.database_path)
            conn.row_factory = sqlite3.Row
            
            conn.execute("SELECT 1 FROM papers LIMIT 1")
            
            papers = conn.execute("""
                SELECT id, filename, content, points 
                FROM papers
            """).fetchall()
            
            response = {
                "status": "success",
                "papers": [{
                    "id": p["id"],
                    "filename": p["filename"] or "Untitled",
                    "content_preview": (p["content"][:100] + "...") if p["content"] else "",
                    "content_length": len(p["content"]) if p["content"] else 0,  # Add this line
                    "points": json.loads(p["points"]) if p["points"] else []
                } for p in papers]
            }
            
            return JSONResponse(response)
            
        except sqlite3.OperationalError as e:
            logger.error(f"Database error: {e}")
            return JSONResponse(
                {"status": "error", "message": "Database not initialized"},
                status_code=404
            )
            
        except Exception as e:
            logger.error(f"Unexpected error: {e}")
            return JSONResponse(
                {"status": "error", "message": "Internal server error"},
                status_code=500
            )
            
    finally:
        if conn: conn.close()
frontend_path = "/workspaces/bytewise-module-prototyping-2/frontend"
app.mount("/app", StaticFiles(directory=frontend_path, html=True), name="frontend")

# Initialize database
init_db()

# Frontend setup

os.makedirs(frontend_path, exist_ok=True)
#app.mount("/static", StaticFiles(directory=os.path.join(frontend_path, "static")), name="static")
#app.mount("/assets", StaticFiles(directory=os.path.join(frontend_path, "assets")), name="assets")

"""
class OllamaClient:
    def __init__(self):
        self.base_url = "http://localhost:11434"
        self.model = "llama3:8b-instruct-q4_K_M"
        self.timeout = aiohttp.ClientTimeout(total=120)
        self.session = None  # Persistent session

    async def ensure_session(self):
        if self.session is None or self.session.closed:
            self.session = aiohttp.ClientSession(timeout=self.timeout)

    async def generate(self, prompt: str, max_tokens: int = 500) -> str:
        try:
            await self.ensure_session()
            payload = {
                "model": self.model,
                "prompt": prompt,
                "stream": False,
                "options": {
                    "temperature": 0.7,
                    "num_ctx": 4096,
                    "max_tokens": max_tokens
                }
            }
            async with self.session.post(
                f"{self.base_url}/api/generate",
                json=payload
            ) as response:
                if response.status != 200:
                    error = await response.text()
                    logger.error(f"Ollama error: {error}")
                    return ""
                return (await response.json()).get("response", "").strip()
        except Exception as e:
            logger.error(f"LLM connection failed: {str(e)}")
            return ""

llm = OllamaClient()
"""

# Initialize OpenAI client
client = AsyncOpenAI(
  base_url="https://openrouter.ai/api/v1",
  api_key="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",
)



@app.post("/upload")
async def upload_file(file: UploadFile = File(...)):
    if file.content_type != "application/pdf":
        raise HTTPException(400, "Only PDF files are allowed")

    temp_dir = tempfile.mkdtemp()
    pdf_id = str(uuid.uuid4())
    pdf_path = os.path.join(temp_dir, f"{pdf_id}.pdf")
    
    try:
        # Save uploaded file
        with open(pdf_path, "wb") as f:
            f.write(await file.read())

        # Process with Mineru
        output_dir = os.path.join(settings.mineru_output_dir, pdf_id)
        os.makedirs(output_dir, exist_ok=True)
        text_content = process_pdf_with_mineru(pdf_path, output_dir)

        # Generate points with strict formatting
        prompt = f"""Extract exactly 5 key research points from this paper.
        Format each point EXACTLY like this:
        - [concise point here]
        Include nothing else in your response.

        Paper content:
        {text_content[:4000]}"""

        #response = await llm.generate(prompt, max_tokens=800)
        response = await client.chat.completions.create(

            #model="anthropic/claude-sonnet-4",
            model = "deepseek/deepseek-chat-v3-0324:free",
            messages=[
                {
                    "role": "user",
                    "content": prompt
                }
            ]
        )

        # Process and clean points
        raw_points = []
        if response:
            raw_points = [
                line.strip() 
                for line in response.choices[0].message.content.split('\n') 
                if line.strip().startswith('-') and len(line.strip()) > 2
            ]
        
        # Ensure we have exactly 5 points
        points = []
        for i in range(5):
            if i < len(raw_points):
                # Clean the point
                point = raw_points[i].strip()
                if point.startswith('- '):
                    point = point[2:]
                points.append(f"- {point}")
            else:
                # Fallback points
                points.append(f"- Key point {i+1} from research")

        # Prepare response
        formatted_points = []
        for point in points:
            formatted_points.append({
                "text": point,
                "source": file.filename,
                "sourceId": pdf_id
            })

        # Store in database (store as JSON string)
        store_paper(pdf_id, file.filename, text_content, json.dumps(points))

        return JSONResponse({
            "status": "success",
            "points": formatted_points,
            "id": pdf_id,
            "filename": file.filename
        })

    except Exception as e:
        logger.error(f"Upload failed: {str(e)}")
        raise HTTPException(500, f"Processing failed: {str(e)}")
    finally:
        # Clean up temp files
        try:
            if os.path.exists(pdf_path):
                os.unlink(pdf_path)
            if os.path.exists(temp_dir):
                os.rmdir(temp_dir)
        except Exception as e:
            logger.warning(f"Temp file cleanup failed: {str(e)}")

@app.get("/download/{paper_id}")
async def download_markdown(paper_id: str):
    try:
        # Check if paper exists in database
        conn = sqlite3.connect(settings.database_path)
        c = conn.cursor()
        c.execute("SELECT filename FROM papers WHERE id = ?", (paper_id,))
        paper = c.fetchone()
        conn.close()
        
        if not paper:
            raise HTTPException(404, "Paper not found")
        
        # Look for markdown file in mineru output directory
        paper_dir = os.path.join(settings.mineru_output_dir, paper_id)
        if not os.path.exists(paper_dir):
            raise HTTPException(404, "Paper directory not found")
        
        # Search for markdown files in the paper directory
        md_files = []
        for root, _, files in os.walk(paper_dir):
            for file in files:
                if file.endswith('.md'):
                    md_files.append(os.path.join(root, file))
        
        if not md_files:
            raise HTTPException(404, "No markdown files found in paper directory")
        
        # Use the first markdown file found (you might want to add more specific logic if needed)
        md_path = md_files[0]
        md_filename = os.path.basename(md_path)
        
        return FileResponse(
            md_path,
            media_type="text/markdown",
            filename=md_filename
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Download failed: {str(e)}")
        raise HTTPException(500, "Failed to download markdown file")

@app.post("/generate")
async def generate_points(request: GenerateRequest):
    try:
        # Get relevant papers with better filtering
        relevant_papers = await get_relevant_papers_by_points(request.prompt)
        if not relevant_papers:
            return {
                "status": "success",
                "points": [{
                    "formatted_text": "No relevant papers found",
                    "clean_source": ""
                }]
            }

        # Prepare unified context
        context = "\n\n".join(
            f"PAPER {i+1}: {p['filename']}\n"
            f"KEY POINTS:\n" + "\n".join(p['points'][:5]) + "\n"
            f"CONTENT EXCERPT:\n{p['content'][:4000]}..."
            for i, p in enumerate(relevant_papers[:len(relevant_papers)-1])  # Top N papers
        )

        # Enhanced prompt with strict formatting rules
        prompt = f"""Generate 5-7 high-quality research points about: {request.prompt}
        
        Using these papers as sources:
        {context}
        
        FORMATTING RULES:
        1. Each point must start with • 
        2. Include (Source: Paper X | *name of the paper and its author* | APA 7 Citation of paper) where X is the paper number
        3. Keep points concise but informative
        4. Compare/contrast different papers when relevant
        5. Cover different aspects of the topic
        
        EXAMPLE:
        • GPT-3 demonstrates self-referential capabilities (Source: *title of the paper used*)
        • Current limitations include... (Source: *title of the paper used*)
        • Compared to *title of the paper used*, *title of the paper used* shows... (Sources: *title of the paper used*, *title of the paper used*)"""

        # Single LLM call for all points (more efficient)
        response = await client.chat.completions.create(
            model="deepseek/deepseek-chat-v3-0324:free",
            messages=[{"role": "user", "content": prompt}],
            temperature=0.7,
            max_tokens=2000
        )

        # Process response with better error handling
        if not response.choices:
            raise ValueError("No response generated from LLM")

        raw_points = [
            line.strip() 
            for line in response.choices[0].message.content.split('\n') 
            if line.strip().startswith('•')
        ][:7]  # Limit to 7 best points

        # Map sources back to paper IDs
        paper_map = {
            f"Paper{i+1}": p['id']
            for i, p in enumerate(relevant_papers[:3])
        }

        points = []
        for line in raw_points:
            # Extract source papers
            source_matches = re.findall(r'\(Source: (Paper\d+(?:, Paper\d+)*)\)', line)
            source_papers = []
            if source_matches:
                for match in source_matches[0].split(', '):
                    if match in paper_map:
                        source_papers.append({
                            'id': paper_map[match],
                            'name': next(
                                p['filename'] for p in relevant_papers 
                                if p['id'] == paper_map[match]
                            )
                        })

            # Format the point
            point_text = line.split('• ')[1].split(' (Source:')[0].strip()
            
            points.append({
                "formatted_text": line,
                "raw_data": {
                    "text": point_text,
                    "sources": source_papers,
                    "source": ", ".join(p['name'] for p in source_papers),
                    "sourceIds": [p['id'] for p in source_papers]
                }
            })

        return {
            "status": "success", 
            "points": points,
            "paper_ids": list(paper_map.values())  # Track used papers
        }

    except Exception as e:
        logger.error(f"Generation failed: {str(e)}")
        return {
            "status": "error",
            "points": [{
                "formatted_text": f"Error: {str(e)}",
                "clean_source": ""
            }]
        }

# Database endpoints (must come before catch-all)
# main.py

        
@app.get("/database/{paper_id}")
async def view_paper_details(paper_id: str):
    try:
        conn = sqlite3.connect(settings.database_path)
        conn.row_factory = sqlite3.Row
        c = conn.cursor()
        c.execute("SELECT id, filename, content, points FROM papers WHERE id = ?", (paper_id,))
        paper = c.fetchone()
        conn.close()
        
        if not paper:
            raise HTTPException(404, "Paper not found")
            
        points = (json.loads(paper["points"]) 
                 if paper["points"] and paper["points"].startswith('[') 
                 else (paper["points"] if paper["points"] else []))
        
        return {
            "id": paper["id"],
            "filename": paper["filename"],
            "content": paper["content"],
            "points": points
        }
    except Exception as e:
        logger.error(f"Paper details failed: {str(e)}")
        raise HTTPException(500, str(e))
    
@app.post("/discuss")
async def discuss_points(request: GenerateRequest):
    try:
        # Get previously used papers or find new relevant ones
        if hasattr(request, 'paper_ids') and request.paper_ids:
            papers = []
            conn = sqlite3.connect(settings.database_path)
            for paper_id in request.paper_ids:
                paper = conn.execute(
                    "SELECT id, filename, content, points FROM papers WHERE id = ?",
                    (paper_id,)
                ).fetchone()
                if paper:
                    papers.append(dict(paper))
            conn.close()
        else:
            papers = await get_relevant_papers_by_points(request.prompt)

        if not papers:
            return {
                "status": "success",
                "discussion": "No relevant papers found for discussion"
            }

        # Prepare discussion context
        context = "\n\n".join(
            f"PAPER {i+1} - {p['filename']}:\n"
            f"KEY POINTS:\n" + "\n".join(
                json.loads(p['points']) if isinstance(p['points'], str) 
                else p['points']
            ) + "\n"
            f"CONTENT EXCERPT:\n{p['content'][:4000]}..."
            for i, p in enumerate(papers)
        )

        discussion_prompt = f"""Generate a comprehensive discussion about: {request.prompt}
        
        Using these research papers:
        {context}
        
        GUIDELINES:
        1. Start with an overview of key findings
        2. Compare and contrast different papers
        3. Highlight areas of agreement/disagreement
        4. Discuss limitations and future directions
        5. Use [1], [2], etc. for citations matching the paper numbers above
        6. Maintain academic tone but avoid jargon
        7. Length: ~500 words"""

        response = await client.chat.completions.create(
            model="deepseek/deepseek-chat-v3-0324:free",
            messages=[{"role": "user", "content": discussion_prompt}],
            temperature=0.7,
            max_tokens=2000
        )

        return {
            "status": "success",
            "discussion": response.choices[0].message.content,
            "sources": [p['filename'] for p in papers]
        }

    except Exception as e:
        logger.error(f"Discussion failed: {str(e)}")
        return {
            "status": "error",
            "discussion": f"Error generating discussion: {str(e)}"
        }

# Keep this at the BOTTOM of your routes:
@app.get("/")
async def root():
    return FileResponse(os.path.join(frontend_path, "index.html"))

app.mount("/static", StaticFiles(directory=os.path.join(frontend_path, "static")), name="static")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="127.0.0.1", port=8000)