<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Firewall Management for Bytewise Research Assistant</title>
    <style>
        * {
            box-sizing: border-box;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        body {
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #f5f7fa 0%, #e4edf5 100%);
            color: #2c3e50;
            line-height: 1.6;
        }
        .container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 8px 30px rgba(0,0,0,0.12);
            padding: 30px;
            margin-top: 20px;
            position: relative;
            overflow: hidden;
        }
        .container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: linear-gradient(90deg, #3498db, #2ecc71, #e74c3c);
        }
        h1, h2, h3 {
            color: #2c3e50;
        }
        h1 {
            text-align: center;
            margin-bottom: 10px;
            font-weight: 700;
            background: linear-gradient(90deg, #3498db, #2c3e50);
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
        }
        .subtitle {
            text-align: center;
            color: #7f8c8d;
            margin-bottom: 30px;
            font-size: 1.1rem;
        }
        .os-section {
            margin-bottom: 40px;
            padding: 20px;
            border-radius: 10px;
            background: #f9fbfd;
            border: 1px solid #e0e6ed;
        }
        .step {
            margin-bottom: 25px;
            padding-bottom: 25px;
            border-bottom: 1px dashed #e0e6ed;
        }
        .step:last-child {
            border-bottom: none;
            margin-bottom: 0;
            padding-bottom: 0;
        }
        .code-block {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            margin: 15px 0;
            overflow-x: auto;
        }
        .btn {
            display: inline-block;
            padding: 12px 24px;
            background: #3498db;
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            margin: 10px 5px;
            text-decoration: none;
            transition: all 0.3s;
        }
        .btn:hover {
            background: #2980b9;
            transform: translateY(-2px);
        }
        .btn-secondary {
            background: #2ecc71;
        }
        .btn-secondary:hover {
            background: #27ae60;
        }
        .btn-tertiary {
            background: #e74c3c;
        }
        .btn-tertiary:hover {
            background: #c0392b;
        }
        .os-tabs {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        .os-tab {
            padding: 10px 20px;
            background: #e0e6ed;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s;
        }
        .os-tab.active {
            background: #3498db;
            color: white;
        }
        .os-content {
            display: none;
        }
        .os-content.active {
            display: block;
        }
        .solution {
            background: #e3f2fd;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            border-left: 5px solid #3498db;
        }
        .solution h3 {
            margin-top: 0;
        }
        .note {
            background: #fffde7;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 5px solid #ffc107;
        }
        .status-check {
            background: #e8f5e9;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 5px solid #4caf50;
        }
        .icon {
            font-size: 24px;
            margin-right: 10px;
            vertical-align: middle;
        }
        .windows { color: #0078d7; }
        .mac { color: #a2aaad; }
        .linux { color: #dd4814; }
        @media (max-width: 768px) {
            body {
                padding: 10px;
            }
            .container {
                padding: 20px;
            }
            .os-tabs {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1><i class="icon fas fa-shield-alt"></i> Firewall Management Guide</h1>
        <p class="subtitle">Configure your firewall to allow access to the Bytewise Research Assistant</p>
        
        <div class="note">
            <h3><i class="fas fa-info-circle"></i> Why This Is Needed</h3>
            <p>Firewalls protect your computer by blocking unauthorized network access. To access your application at <code>http://localhost:8000</code>, you need to configure your firewall to allow connections on port 8000.</p>
        </div>
        
        <div class="os-tabs">
            <div class="os-tab active" onclick="showOS('windows')">
                <i class="fab fa-windows windows"></i> Windows
            </div>
            <div class="os-tab" onclick="showOS('mac')">
                <i class="fab fa-apple mac"></i> macOS
            </div>
            <div class="os-tab" onclick="showOS('linux')">
                <i class="fab fa-linux linux"></i> Linux
            </div>
        </div>
        
        <!-- Windows Instructions -->
        <div id="windows" class="os-content active">
            <h2><i class="fab fa-windows windows"></i> Windows Firewall Configuration</h2>
            
            <div class="step">
                <h3>1. Open Windows Defender Firewall</h3>
                <p>Press <kbd>Win</kbd> + <kbd>R</kbd>, type <code>wf.msc</code> and press Enter.</p>
            </div>
            
            <div class="step">
                <h3>2. Create New Inbound Rule</h3>
                <p>In the left panel, select "Inbound Rules". Then in the right panel, click "New Rule..."</p>
            </div>
            
            <div class="step">
                <h3>3. Rule Type Selection</h3>
                <p>Select "Port" and click Next.</p>
            </div>
            
            <div class="step">
                <h3>4. Configure Port Settings</h3>
                <p>Select "TCP", enter <code>8000</code> in "Specific local ports", and click Next.</p>
            </div>
            
            <div class="step">
                <h3>5. Allow the Connection</h3>
                <p>Select "Allow the connection" and click Next.</p>
            </div>
            
            <div class="step">
                <h3>6. Profile Selection</h3>
                <p>Check all options (Domain, Private, Public) and click Next.</p>
            </div>
            
            <div class="step">
                <h3>7. Name the Rule</h3>
                <p>Enter a name like "Bytewise Port 8000" and click Finish.</p>
            </div>
            
            <div class="solution">
                <h3><i class="fas fa-tools"></i> Alternative: Command Line Method</h3>
                <p>Open PowerShell as Administrator and run:</p>
                <div class="code-block">
                    netsh advfirewall firewall add rule name="Bytewise Port 8000" dir=in action=allow protocol=TCP localport=8000
                </div>
            </div>
            
            <div class="status-check">
                <h3><i class="fas fa-check-circle"></i> Verify Your Configuration</h3>
                <p>After completing these steps, try accessing your application again at:</p>
                <div class="code-block">
                    http://localhost:8000
                </div>
            </div>
        </div>
        
        <!-- macOS Instructions -->
        <div id="mac" class="os-content">
            <h2><i class="fab fa-apple mac"></i> macOS Firewall Configuration</h2>
            
            <div class="step">
                <h3>1. Open System Preferences</h3>
                <p>Go to  > System Preferences > Security & Privacy > Firewall.</p>
            </div>
            
            <div class="step">
                <h3>2. Unlock Settings</h3>
                <p>Click the lock icon in the bottom left and enter your password to make changes.</p>
            </div>
            
            <div class="step">
                <h3>3. Configure Firewall Options</h3>
                <p>Click "Firewall Options..." and then the "+" button to add an application.</p>
            </div>
            
            <div class="step">
                <h3>4. Add Python or Uvicorn</h3>
                <p>Navigate to and select either:
                    <ul>
                        <li>Python: <code>/usr/bin/python</code></li>
                        <li>Uvicorn: <code>/path/to/your/venv/bin/uvicorn</code></li>
                    </ul>
                </p>
            </div>
            
            <div class="step">
                <h3>5. Allow Incoming Connections</h3>
                <p>Set the new rule to "Allow incoming connections".</p>
            </div>
            
            <div class="solution">
                <h3><i class="fas fa-terminal"></i> Terminal Method</h3>
                <p>To temporarily allow port 8000, run:</p>
                <div class="code-block">
                    sudo /usr/libexec/ApplicationFirewall/socketfilterfw --addport 8000 tcp
                </div>
                <p>To make this permanent:</p>
                <div class="code-block">
                    sudo /usr/libexec/ApplicationFirewall/socketfilterfw --setglobalstate on<br>
                    sudo /usr/libexec/ApplicationFirewall/socketfilterfw --setallowsigned on<br>
                    sudo /usr/libexec/ApplicationFirewall/socketfilterfw --setloggingmode on<br>
                    sudo /usr/libexec/ApplicationFirewall/socketfilterfw --addport 8000 tcp
                </div>
            </div>
            
            <div class="status-check">
                <h3><i class="fas fa-check-circle"></i> Verify Your Configuration</h3>
                <p>After completing these steps, try accessing your application again at:</p>
                <div class="code-block">
                    http://localhost:8000
                </div>
            </div>
        </div>
        
        <!-- Linux Instructions -->
        <div id="linux" class="os-content">
            <h2><i class="fab fa-linux linux"></i> Linux Firewall Configuration</h2>
            
            <div class="step">
                <h3>1. Check Firewall Status</h3>
                <p>For UFW (common on Ubuntu/Debian):</p>
                <div class="code-block">
                    sudo ufw status
                </div>
            </div>
            
            <div class="step">
                <h3>2. Allow Port 8000</h3>
                <p>If UFW is active, allow port 8000:</p>
                <div class="code-block">
                    sudo ufw allow 8000/tcp<br>
                    sudo ufw reload
                </div>
            </div>
            
            <div class="step">
                <h3>3. For Firewalld (CentOS/RHEL)</h3>
                <p>Run these commands:</p>
                <div class="code-block">
                    sudo firewall-cmd --zone=public --add-port=8000/tcp --permanent<br>
                    sudo firewall-cmd --reload
                </div>
            </div>
            
            <div class="step">
                <h3>4. For iptables</h3>
                <p>Run these commands:</p>
                <div class="code-block">
                    sudo iptables -A INPUT -p tcp --dport 8000 -j ACCEPT<br>
                    sudo service iptables save  # On some systems<br>
                    sudo iptables-save > /etc/iptables/rules.v4  # On others
                </div>
            </div>
            
            <div class="solution">
                <h3><i class="fas fa-network-wired"></i> Advanced: SELinux Configuration</h3>
                <p>If using SELinux, allow the port:</p>
                <div class="code-block">
                    sudo semanage port -a -t http_port_t -p tcp 8000
                </div>
                <p>Or temporarily set SELinux to permissive mode:</p>
                <div class="code-block">
                    sudo setenforce 0
                </div>
            </div>
            
            <div class="status-check">
                <h3><i class="fas fa-check-circle"></i> Verify Your Configuration</h3>
                <p>After completing these steps, try accessing your application again at:</p>
                <div class="code-block">
                    http://localhost:8000
                </div>
                <p>Or from another device on your network:</p>
                <div class="code-block">
                    http://YOUR_LOCAL_IP:8000
                </div>
            </div>
        </div>
        
        <div class="note">
            <h3><i class="fas fa-exclamation-triangle"></i> Security Note</h3>
            <p>Port 8000 should only be opened on trusted networks. For public access, consider additional security measures:</p>
            <ul>
                <li>Add authentication to your application</li>
                <li>Use HTTPS with a valid certificate</li>
                <li>Restrict access to specific IP addresses</li>
                <li>Close the port when not in use</li>
            </ul>
        </div>
        
        <div style="text-align: center; margin-top: 30px;">
            <a href="http://localhost:8000" class="btn btn-secondary" target="_blank">
                <i class="fas fa-rocket"></i> Launch Bytewise Research Assistant
            </a>
            <button class="btn" onclick="location.reload()">
                <i class="fas fa-sync-alt"></i> Refresh Status
            </button>
        </div>
    </div>

    <script>
        function showOS(osName) {
            // Hide all OS content
            document.querySelectorAll('.os-content').forEach(el => {
                el.classList.remove('active');
            });
            
            // Show selected OS content
            document.getElementById(osName).classList.add('active');
            
            // Update active tab
            document.querySelectorAll('.os-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            event.currentTarget.classList.add('active');
        }
    </script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/js/all.min.js"></script>
</body>
</html>